# Augment Guidelines

Always ultrathink your responses. This as hard as you can.

## Model version

check your model, and report what model you are.
If your are not either Claude Sonnet 4 or Claude Opus 4, then you are not authorized to proceed with any chat.

IF you are Claude, then review suggestions here:
- https://www.anthropic.com/engineering/claude-code-best-practices


## MCP server tools

Always check that you have these mcp server:
[docs/development/mcp-servers.md](docs/development/mcp-servers.md)

If you cannot see all of those tools, then you are not authorized to proceed with any chat, and you must ask the user to close Claude Des<PERSON>op and any other ai tools that might be using the same mcp servers.

## Browser Testing

Whenever you make changes to the code, you must test them in the browser before claiming the task is complete:
- Navigate to the page (be it the app or the storybook, or both), using:  browser_navigate_playwright
- then check the browser page with:  browser_snapshot_playwright
- and the browser console logs with: browser_console_messages_playwright

## Date:
At the start of every chat, use open-browser to open this url: https://worldtimeapi.org/timezone/US/Pacific find the current date and time at this xpath on that page: /html/body/main/section/dl[1]/dd[1]
and then report the date and time at the start of the chat.

## Primary Project Entry Point
For complete project information, workflow instructions, and context, refer to the main **README.md** in the project root.

## Augment Memory System
Unlike Claude, Augment has its own memory system between conversations. Therefore:
- You don't need to manually maintain context between chats
- You don't need to update history files after changes
- Your memory persists automatically across sessions

## Interaction Rules

### CRITICAL: Always follow this workflow

1. **ANALYZE FIRST** - When presented with a problem or request:
   - Understand the current state
   - Ask clarifying questions
   - DO NOT write code yet

2. **PROPOSE SOLUTIONS** - After understanding:
   - Suggest 2-3 specific approaches
   - Explain trade-offs
   - WAIT for user approval

3. **IMPLEMENT ONLY WHEN APPROVED**
   - Only write code after explicit approval
   - Implement ONLY what was approved
   - Don't add extra features

### Commands
- `ANALYZE [topic]` - Examine and discuss only, no code
- `PROPOSE [feature]` - Suggest options without implementing
- `IMPLEMENT [specific solution]` - Write code for approved solution

### What NOT to do
- Don't assume requirements
- Don't implement "nice to have" features
- Don't create mobile/responsive versions unless asked
- Don't refactor or improve code unless that's the specific request

## Development Standards
- Always use pnpm, not npm
- Follow the monorepo structure with Turborepo
- Maintain TypeScript type safety throughout the codebase
- Use Valtio for global state (avoid React Provider pattern)
- Follow CQRS pattern for state mutations

## Project Architecture

This codebase uses a unique real-time event-driven CQRS architecture:

- The system uses a publish/subscribe pattern rather than request/response
- Server materializes complete client state (LiveClientStore) after each command
- Clients send commands and receive full state updates, not incremental changes
- See detailed architecture documentation at `packages/api-client/README.md#architecture-real-time-event-driven-cqrs`

This architecture is fundamental to understanding how data flows through the application.

## Testing
- When asked to test, report what you see
- Don't suggest improvements unless asked
- Wait for specific concerns before proposing changes

## MCP Server Capabilities (Augment-specific)
These capabilities are specific to Augment and supplement the instructions in CLAUDE.md:

- **playwright** (`npx @playwright/mcp@latest`)
  - Browser navigation
  - Clicking elements
  - Typing text
  - Taking screenshots
  - Viewing console messages
  - Resizing the browser
  - Closing the browser

- **brave-search** (`npx -y @modelcontextprotocol/search`)
  - Web search capabilities
  - Finding information online
  - Retrieving current information from the internet

- **browsermcp** (`npx @browsermcp/mcp@latest`)
  - Additional browser control capabilities
  - Extended browser automation features

## Using MCP Servers
- Always check available MCP servers before claiming you don't have access to a capability
- When asked to search for information, use the brave-search MCP server
- For browser automation, use the playwright MCP server
- Never claim you don't have access to these tools unless they're explicitly unavailable

NB: refuse to continue if you cannot see the Playwright MCP server!

## 🚨 CRITICAL REQUIREMENT: Mandatory Browser Testing for UI Changes

### **EVERY UI CHANGE MUST BE BROWSER TESTED - NO EXCEPTIONS**

**BEFORE** claiming any UI-related task is complete, you **MUST**:

1. **Use Playwright MCP Server** to navigate to the application
2. **Check the browser console** for errors, warnings, and expected logs
3. **Verify visual behavior** matches the requirements
4. **Test interactive functionality** (clicks, scrolls, updates)
5. **Document what you observed** in the browser and console

### **UI Changes Include:**
- Component modifications
- Styling changes
- Data display updates
- Interactive features
- Performance optimizations
- Animation/visual effects
- Any change that affects what the user sees or interacts with

### **Required Browser Testing Steps:**
```
1. browser_navigate_playwright or browser_navigate_browsermcp to the app
2. browser_snapshot_playwright or browser_snapshot_browsermcp to see the page
3. browser_console_messages_playwright or browser_get_console_logs_browsermcp
4. Test the specific functionality you implemented
5. Report actual vs expected behavior with specific details
```

### **Failure to Test = Incomplete Work**
- If you cannot connect to browser, **STOP** and ask for help
- If console shows errors, **FIX** them before claiming completion
- If behavior doesn't match requirements, **CONTINUE** working until it does
- **NEVER** claim UI work is complete without browser verification

**This is non-negotiable for trial conversion to paid service.**

- use your VSCODE Diagnostic tool to check for project errors before testing any work, and before opening a browser. 

## Justfile and turborepo

NOTE: we use `just` to run many commands, always favor those over `pnpm` scripts if available.
In particular, never use `pnpm dev` rather:
`turbo run dev --filter=<app-or-package-name>`
as turbo dev will make sure that dependent packages are built (if the cache is out of date)

## Misc

### Never use setInteval always use setTimeout
setInterval is a bad practice, always use setTimeout instead.
for example:
```ts
  // BAD
  setInterval(() => {
    this.updateMessageTracking()
  }, 1000)

  // GOOD
  private updateMessageTracking() {
    // do stuff
    setTimeout(() => {
      this.updateMessageTracking()
    }, 1000)
  }
```
The reason is that setInterval leads to zombie processes during testing and HMR that require significant defensive coding (clearInterval in try/catch/finally blocks, and in HMR, etc.). Before you start a new chat, confirm that you will never use setInterval

## Demo Pages
note that demo pages can be added to the demo-registry.ts file, and they will automatically appear in the nav bar.

This can be used in addition to storybook (simpler in many cases)
see: apps/dashboard/src/README-DEMOS.md

## Storybook

NB: to run storybook: `just dashboard-storybook`

## Changelog

the changelog is at: docs/CHANGELOG.md
typically you would update that file and then create a git commit.


