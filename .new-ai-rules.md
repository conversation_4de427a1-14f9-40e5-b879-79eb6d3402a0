# AI Rules for React Component Development

## Core Mission

Create production-ready, fully-featured React components that adapt existing Vue.js components to support auction mechanisms from `docs/design/A.auction-rules.md`. Use TypeScript with types from `apps/dashboard/src/api-client/connector/types/generated.ts`.

## Behavioral Guidelines

### Analysis Phase (ALWAYS START HERE)
- When given a Vue component to adapt, first analyze its structure, props, events, and functionality
- Identify which parts translate directly to React and which need architectural changes
- Ask specific clarifying questions about:
  - Which Vue component props should become React props
  - How Vue events should be handled in React (callbacks, state updates)
  - Whether sub-components should be separate React components or inline JSX
  - Any auction-specific business logic requirements
- DO NOT start coding until you understand the requirements completely

### Proposal Phase (REQUIRED BEFORE IMPLEMENTATION)
- Present 2-3 specific architectural approaches for the React component
- Explain trade-offs between approaches (e.g., composition vs props, local vs global state)
- Outline the component structure including:
  - Main props interface
  - State management strategy (prefer Valtio for global state)
  - Key functions and event handlers
  - Sub-component organization
- Wait for explicit approval before proceeding

### Communication Style
- Ask direct, specific questions rather than general ones
- When uncertain about Vue→React mappings, request clarification with examples
- Explain your reasoning for architectural decisions
- Be explicit about what you're implementing vs what you're omitting

## Implementation Standards

### Props & Types
- Mirror Vue component props in React, but adapt for React patterns (e.g., onEvent callbacks instead of Vue $emit)
- Use types from `apps/dashboard/src/api-client/connector/types/generated.ts` for all auction/API-related data
- For sub-components: analyze whether they should be separate React components or inline JSX based on reusability and complexity

### Styling & Visual Fidelity
- Replicate the Vue component's visual appearance and behavior exactly
- Include hover states, transitions, and micro-interactions from the original
- Check existing project styling approach (Tailwind, CSS modules, etc.) and follow those conventions
- Apply design principles: hierarchy, contrast, balance, and movement

### State Management
- Use Valtio for global state (avoid React Provider pattern)
- Follow CQRS pattern for state mutations
- Local React state is acceptable for UI-only concerns (modals, form inputs, etc.)
- Never use `setInterval` - always use `setTimeout` for recurring tasks

### Auction Logic Integration
- Ensure components correctly implement behaviors from `docs/design/A.auction-rules.md`
- This includes bid constraints, price movement rules, and auction termination logic
- Ask for clarification if auction rules seem to conflict with component requirements

## Required Deliverables

For each PRIMARY component (not sub-components unless they're complex enough to warrant separate demonstration):

1. **`ComponentName.tsx`** - Main component file
2. **`ComponentNameDemo.tsx`** - Demo showcasing various states and prop configurations
3. **`ComponentName.stories.tsx`** - Storybook file with different scenarios

## Quality Standards

- Create production-ready, fully-featured implementations
- Include thoughtful details like hover states, transitions, and micro-interactions
- Write clean, maintainable, performant code
- Use parallel tool operations when possible for efficiency
- Clean up any temporary files created during development

## Testing Requirements

- Check VSCode diagnostics for errors before testing
- Use Playwright MCP server for browser testing when available, using both the screenshot and console message log mcp tools.
- Test in Storybook using `just dashboard-storybook`
- Test in the browser using `just dashboard`
- Verify visual behavior matches requirements
- Test interactive functionality thoroughly

## Key Reference Files

- Auction Rules: `docs/design/A.auction-rules.md`
- API Types: `apps/dashboard/src/api-client/connector/types/generated.ts`
- Development Guidelines: `.augment-guidelines.md`
- Prompt Best Practices: `.claude-prompt-best-practises`