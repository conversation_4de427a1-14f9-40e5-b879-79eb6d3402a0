# Auction Chat Component Requirements

## Overview

The Auction Chat component provides real-time messaging functionality for auction participants, enabling communication between traders, auctioneers, and the system during live auction sessions.

## Component Architecture

### Core Component: `AuctionChat`
- **Location**: `apps/dashboard/src/components/chat/AuctionChat.tsx`
- **Demo**: `apps/dashboard/src/components/chat/AuctionChatDemo.tsx`
- **Width**: 400px (compact design for sidebar/panel usage)

## Props Interface

```typescript
interface AuctionChatProps {
  is_auctioneer: boolean;        // User role determines UI behavior
  messages: MessageElement[];   // Array of chat messages
  outer_height: number;         // Container height in pixels
  width: number;               // Container width in pixels (400px recommended)
  onSubmitMessage?: (message: string) => void; // Message submission callback
}
```

## Message Types & Communication Rules

### Message Flow Rules
1. **Traders**: Can only send messages to the auctioneer
2. **Auctioneers**: Can only send messages to all participants (broadcast)
3. **System**: Can send messages to specific traders, all traders, or auctioneer only

### Message Types (AuMessageType enum)
- `TRADER_TO_AUCTIONEER`: Individual trader questions/comments
- `AUCTIONEER_BROADCAST`: Auctioneer announcements to all participants
- `SYSTEM_BROADCAST`: System announcements to all participants
- `SYSTEM_TO_TRADER`: System notifications to specific traders
- `SYSTEM_TO_AUCTIONEER`: System alerts to auctioneer only

## Message Format

### MessageElement Interface
```typescript
interface MessageElement {
  id: string;                    // Unique message identifier
  from: string;                 // Sender name in format "username (Company Name)"
  to: string;                   // Recipient ("Auctioneer", "All", or specific user)
  message: string;              // Message content
  message_type: AuMessageType;  // Message type enum
  message_type_label: string;   // Human-readable type label
  timestamp: number;            // Unix timestamp
  timestamp_label: string;      // Formatted time display
}
```

### Username Format
- **Traders**: `"username (Company Longname)"` (e.g., "jsmith (GreenPower Corp)")
- **Auctioneer**: `"Auctioneer"`
- **System**: `"System"`

## Visual Design

### Layout
- **Compact design**: 400px width, reduced padding and spacing
- **Message alignment**: Outgoing messages right-aligned, incoming left-aligned
- **Message bubbles**: Color-coded by message type with appropriate icons

### Typography
- **Message content**: `text-xs` (12px) for compact display
- **Message headers**: `text-xs` with medium font weight
- **Timestamps**: `text-xs` with reduced opacity, right-aligned

### Color Coding
- **Auctioneer Broadcast**: Blue (`text-blue-600 bg-blue-50 border-blue-200`)
- **Trader to Auctioneer**: Purple (`text-purple-600 bg-purple-50 border-purple-200`)
- **System Broadcast**: Orange (`text-orange-600 bg-orange-50 border-orange-200`)
- **System to Trader**: Red (`text-red-600 bg-red-50 border-red-200`)
- **System to Auctioneer**: Gray (`text-gray-600 bg-gray-50 border-gray-200`)

### Icons (Lucide React)
- **Auctioneer Broadcast**: `Radio` icon
- **Trader to Auctioneer**: `User` icon
- **System messages**: `Settings`, `AlertCircle`, `MessageSquare` icons

## Behavior Requirements

### Auto-Scrolling Requirements
- **Default behavior**: New messages are added to the end of the list and the view automatically scrolls to the bottom to keep the latest message visible.
- **User interaction detection**: Auto-scrolling is paused if the user is actively interacting with the message container (e.g., mouse button held down while dragging the scrollbar, or pointer down within the message area).
- **Message addition during interaction**: Even when auto-scroll is paused due to active user interaction, new messages are still added to the end of the list. The scrollbar will update, but the view will not jump.
- **Resuming auto-scroll**: Once the user finishes their active interaction (e.g., releases the mouse button), the default auto-scroll behavior resumes for subsequent new messages.
- **Smooth scroll on release near bottom**: If the user drags the scrollbar to (or near) the bottom and releases the pointer, the view will smoothly scroll to the very end to ensure a clean final position.
- **No explicit user scroll-up state**: The system does not maintain a persistent "user has scrolled up" state that globally disables auto-scroll. Auto-scroll is only paused during active pointer interaction. If a user scrolls up and releases, the next new message will still trigger auto-scroll.
- **No timeouts for resuming**: Auto-scroll resumption is tied to the end of active user interaction (e.g., `pointerup`), not to timers.
- **No special "new messages" indicators**: Visual cues like "New Messages Below" are not required; the standard scrollbar behavior provides feedback.

### Message Input
- **Textarea**: Multi-line input with auto-resize (1-12 rows)
- **Submission**: Enter key submits (Ctrl/Shift+Enter for new lines)
- **Send button**: Icon button with disabled state when input is empty
- **Clear**: Input clears after successful submission

### Role-Based UI
- **Auctioneer view**: No info message, full message height
- **Trader view**: Info message explaining communication rules
- **Message highlighting**: Trader messages highlighted when viewed by auctioneer

## Animation & Interactions

### Message Appearance
- **Animation library**: Use Framer Motion for smooth animations
- **New message animation**: Use motion `layout` prop and `initial`/`animate` for appearance (e.g., 200ms duration).
- **No staggered delays**: All messages appear immediately without delays
- **Hover effects**: Subtle shadow increase on message hover
- **Performance**: Animations must not interfere with scroll behavior.

### Loading States
- **Empty state**: Centered message with icon when no messages
- **Smooth scrolling**: Auto-scroll to bottom uses `behavior: 'auto'` for immediate jump. `behavior: 'smooth'` may be used for specific user-initiated scroll completions (e.g., dragging to bottom).
- **Animation conflicts**: Avoid animations that conflict with scroll behavior.

## Demo Requirements

### Sample Data
- **15 initial messages**: Realistic auction scenario with energy trading
- **Mixed message types**: All 6 message types represented
- **Realistic content**: Auction-specific terminology and scenarios
- **Timeline**: Messages spread over 5-minute period with proper timestamps

### Interactive Demo
- **Dual views**: Tabs for auctioneer and trader perspectives
- **Live controls**: Add/remove messages, clear all, reset demo
- **Auto-generation**: 70% chance of new message every 2 seconds
- **Real input**: Functional message submission that adds to list

### Demo Features
- **Company variety**: Multiple energy companies with realistic names
- **Message variety**: Questions, announcements, system notifications
- **Realistic flow**: Proper auction communication patterns

## Technical Implementation

### Dependencies
- **React**: Hooks (useState, useEffect, useRef)
- **Framer Motion**: Animation library for message appearance
- **shadcn/ui**: Card, Textarea, Button components
- **Lucide React**: Icon components
- **Tailwind CSS**: Styling and responsive design
- **TypeScript**: Full type safety

### State Management
- **Local state**: `inputMessage` (for the text area).
- **Refs**:
    - `messagesContainerRef`: Attached to the scrollable div containing messages.
    - `isPointerDownRef`: A boolean ref to track if the user's pointer (mouse/touch) is currently pressed down within the message container.
    - `isNearBottomRef`: A boolean ref, updated on scroll, to track if the scroll position is near the bottom of the message list.
- **Props**: `messages` array is managed by the parent component.

### Scroll Behavior Implementation
- **Message addition**: New messages are always added to the end of the `messages` array (managed by parent) and rendered at the bottom of the DOM list.
- **Active interaction detection**: `pointerdown` event on the `messagesContainerRef` sets `isPointerDownRef.current` to `true`. `pointerup` event (on `window`, to catch releases outside the container) sets it to `false`.
- **Auto-scroll logic**:
    - When new `messages` are received (useEffect dependency), `scrollToBottom()` is called IF `isPointerDownRef.current` is `false`.
    - This ensures auto-scroll happens unless the user is actively interacting (e.g., dragging the scrollbar).
- **Scroll position tracking**: The `onScroll` event handler updates `isNearBottomRef.current`.
- **Scroll on interaction end**: When `pointerup` occurs:
    - `isPointerDownRef.current` is set to `false`.
    - If `isNearBottomRef.current` is `true` (meaning the user might have dragged to the bottom or was already there), `scrollToBottom('smooth')` is called to ensure the view is cleanly at the end.
- **Simplicity**: The approach relies on direct event handling (`pointerdown`, `pointerup`, `scroll`) rather than complex state machines or timeouts for scroll management.

### Performance Considerations
- **Event-based detection**: Scroll control relies on efficient, native browser events.
- **Animation performance**: Fast 200ms animations and Framer Motion's `layout` prop are used to minimize interference with scroll behavior.
- **Memory cleanup**: Event listeners are properly removed in `useEffect` cleanup functions.
- **Minimal state**: Limited use of React state for scroll logic; refs are preferred for mutable values that don't need to trigger re-renders for their own changes.

## Integration Points

### Parent Component Responsibilities
- **Message management**: Maintain `messages` array state.
- **Message submission**: Handle `onSubmitMessage` callback.
- **Real-time updates**: Update `messages` array when new messages arrive from a source (e.g., WebSocket).
- **User context**: Provide `is_auctioneer` flag based on user role.

### API Integration
- **Message sending**: Submit messages via WebSocket or API.
- **Message receiving**: Subscribe to real-time message updates.
- **User identification**: Include user/company information in messages.
- **Timestamp handling**: Server-side timestamp generation recommended.

## Accessibility

### Keyboard Navigation
- **Tab order**: Logical flow through interactive elements.
- **Enter submission**: Standard form submission behavior for the textarea.
- **Escape handling**: Standard browser behavior (e.g., blur input); no custom Escape handling currently.

### Screen Readers
- **ARIA labels**: Ensure appropriate labels for interactive elements (e.g., send button).
- **Message structure**: Use semantic HTML for message content to aid parsing.
- **Live regions**: Consider using `aria-live` regions if new messages need to be announced more proactively to screen reader users, especially when auto-scroll is paused. (Currently not implemented, but a potential enhancement).

## Testing Requirements

### Unit Tests
- **Message rendering**: Verify correct display of different message types, icons, and colors.
- **Scroll behavior**:
    - Test auto-scroll on new messages when user is not interacting.
    - Test that auto-scroll is paused when `pointerdown` is active in the message container.
    - Test that messages are still added to the list when auto-scroll is paused.
    - Test that auto-scroll resumes for new messages after `pointerup`.
    - Test smooth scroll to bottom if user releases pointer near the bottom.
- **Input handling**: Test message submission via Enter key and Send button, input clearing, and disabled state of the button.
- **Role-based display**: Test UI differences for auctioneer vs. trader views (e.g., info message visibility, message highlighting).

### Integration Tests
- **Real-time updates**: Test with a mock or real WebSocket connection feeding messages.
- **Performance**: Test with rapid message arrival and a large number of messages in the list to ensure smooth scrolling and rendering.
- **Cross-browser**: Verify consistent scroll behavior and visual appearance across major supported browsers.

### Demo Testing
- **Auto-generation**: Verify the 2-second interval (approx.) and 70% chance for new message creation.
- **Interactive controls**: Test all demo buttons (add, remove, clear, reset) and ensure they function as expected.
- **Visual verification**: Confirm proper styling, message alignment, and animations in both auctioneer and trader views.


**Key changes in the documentation:**

*   **Auto-Scrolling Requirements:** Completely rewritten to accurately describe the new behavior focusing on "active user interaction" (pointer down) as the condition for pausing auto-scroll, and how it resumes.
*   **State Management:** Updated refs to `isPointerDownRef` and `isNearBottomRef`, removing mentions of a general "user scrolling flag" or "scroll end marker."
*   **Scroll Behavior Implementation:** Detailed the logic involving `pointerdown`, `pointerup`, `isPointerDownRef`, and `isNearBottomRef` for controlling the scroll.
*   Minor tweaks in "Animation & Interactions" and "Performance Considerations" to align with the new approach.
*   Enhanced "Testing Requirements" for scroll behavior to reflect the new specific conditions to test.
*   Slight update to "Accessibility" regarding live regions as a consideration.

