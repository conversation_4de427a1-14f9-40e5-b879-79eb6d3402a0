### **Double-Sided Price-Reversing Clock-Auction: Rules Summary**

This summary encapsulates the essential rules and objectives for a double-sided
clock-auction framework, ensuring a fair and rational bidding process.

### **Key Objectives of the Rules**

- **Ensure Fairness**: Promote a level playing field by preventing bidders from manipulating bids based on price
  movements.
- **Prevent Gaming**: Ensure bidders cannot manipulate their bids irrationally (ie: which would contradict rational
  economic behavior) in
- **Enforce Rational Behavior**: Enforce that bidders adjust their buy/sell quantities rationally in response to price
  changes.
  response to price changes, ie: enforce monotonically changing demand and supply curves.
- **Achieve Market Equilibrium**: Facilitate the auction's progression towards a balanced state where buy and sell
  volumes match.
- **Create the correct incentives**: Crucially these rules need to be constructed using ex-ante rather than ex-post 
  logic. ie: we don't look at a problem and then try to create rules to correct it, rather we look at rules and 
  determine whether they create the incentive for the problem to occur. 

### **Auction Structure**

- **Rounds**: The auction progresses through multiple rounds.
- **Round Price Announcement**: At the start of each round, a specific price is announced.
- **Bid Submissions**: Bidders submit quantities they wish to **buy** or **sell** at the announced price. **No prices
  are submitted by bidders.**
- **No Transactions During Auction**: Bids are merely indications of willingness; actual transactions occur only when
  the auction concludes.

### **Price Adjustment Mechanism**

- **Price Increases**: If total buy volumes exceed total sell volumes, the round price increases in subsequent rounds.
- **Price Decreases**: If total sell volumes exceed total buy volumes, the round price decreases in subsequent rounds.
- **Auction Termination**: The auction ends when:
    - **Equilibrium**: Total buy and sell volumes are equal.
    - **Overshoot**: The buy/sell activity ratio inverses (e.g., buy volume becomes less than sell volume after
      previously being greater, or vice versa).

### **Bidder Rationality Constraints**

To prevent strategic manipulation and ensure rational bidding behavior, the following rules apply:

#### **Buy Rules**

- **No Increasing Buy at Higher Prices**:
    - Bidders **cannot** bid a larger quantiokty to buy at a higher price than they did at a lower price.
- **No Decreasing Buy at Lower Prices**:
    - Bidders **cannot** bid a smaller quantity to buy at a lower price than they did at a higher price.

#### **Sell Rules**

- **No Increasing Sell at Lower Prices**:
    - Bidders **cannot** bid a larger quantity to sell at a lower price than they did at a higher price.
- **No Decreasing Sell at Higher Prices**:
    - Bidders **cannot** bid a smaller quantity to sell at a higher price than they did at a lower price.

#### **Cross-Bid Constraints**

- **Buy Above Sell Price**:
    - Bidders **cannot** indicate a willingness to buy at prices higher than their own sell prices.
- **Sell Below Buy Price**:
    - Bidders **cannot** indicate a willingness to sell at prices lower than their own buy prices.

#### **Constraints Rules**

Given:

- constraints
- order quantity and side (buy or sell)
- price change (increase or descrease)

Then:

- the new constraints can be calculated as follows:

| Order Side | Price Change | Max Buy     | Min Buy     | Min Sell    | Max Sell    |
|------------|--------------|-------------|-------------|-------------|-------------|
| Buy        | Increase     | = order_qty | unchanged   | unchanged   | unchanged   |
| Buy        | Decrease     | unchanged   | = order_qty | = 0         | = 0         |
| Sell       | Increase     | = 0         | = 0         | = order_qty | unchanged   |
| Sell       | Decrease     | unchanged   | unchanged   | unchanged   | = order_qty |

Explanation:

- For Buy orders:
    - Price increases: Shrinks max buy to order quantity (left side shrinks)
    - Price decreases: Zeros out sell side, and sets min buy to order quantity (right side disappears, then min buy
      moves)

- For Sell orders:
    - Price increases: Zeros out buy side, and sets min sell to order quantity (left side disappears, then min sell
      moves)
    - Price decreases: Shrinks max sell to order quantity (right side shrinks)

### **Price Discovery Example**

Here's a concrete example showing how the price discovery mechanism works:

**Scenario**: Starting price 30, pre-reversal increment = 5, post-reversal increment = 1

```
Round 1: Price 30, Buy=120, Sell=80  → demand > supply → increase by 5
Round 2: Price 35, Buy=110, Sell=90  → demand > supply → increase by 5
Round 3: Price 40, Buy=100, Sell=95  → demand > supply → increase by 5 (PRE-OVERSHOOT ROUND)
Round 4: Price 45, Buy=85,  Sell=105 → supply > demand → OVERSHOOT! Reverse direction
Round 5: Price 44, Buy=87,  Sell=103 → supply > demand → decrease by 1
Round 6: Price 43, Buy=89,  Sell=101 → supply > demand → decrease by 1
Round 7: Price 42, Buy=91,  Sell=99  → supply > demand → decrease by 1
Round 8: Price 41, Buy=93,  Sell=97  → supply > demand → STOP (just before pre-overshoot price of 40)
```

**Key Points**:
- Price moves in **large steps** (5) until overshoot occurs
- After overshoot, price reverses in **small steps** (1)
- Auction **must stop before** reaching the pre-overshoot price (40) to prevent repeating previous state
- If supply > demand in Round 1, the process works in reverse (decreasing by 5, then increasing by 1)

### Auction Ending rule

The auction ends in one of two scenarios:

**Equal Activity Ending**:
- Price moving (let's say up because buy > sell)
- We reach a price where buy = sell
- Auction ends because any further price increase would make buy < sell
- (Same logic for downward movement)

**Overshoot Return Ending**:
- Price moving (let's say up because buy > sell)
- Reaches a price where buy < sell (overshoots)
- Price direction reverses downward
- Must end before reaching the pre-reversal price, as reaching that price would repeat a previous state
- (Same logic for downward movement)

In both cases, once we hit these conditions, there can be no further improvement in matching between buyers and sellers.

### Allocation Round determination Rule

"Take the first round that achieved the highest total matched quantity, whether that's before or after price reversal"

1. The uniform price aspect indeed requires all allocations to be in the same round
 - different rounds having different prices making this obvious

2. The allocation round determination rule is focused on incentives:

- If we were to take the last round of equal matches, we'd create perverse incentives
- Sellers could withhold quantity hoping for higher prices
- Buyers could withhold quantity hoping for lower prices (in the reversal case)
- By taking the first round where we achieve the maximum match, we incentivize early truthful revelation of quantities

3. And this same logic holds after price reversal:

- If price was increasing (buy > sell) then reversed
- We're now waiting for buyers to increase quantities as price drops
- Taking the first round of maximum match after reversal still incentivizes truthful early revelation
- Taking a later round would incentivize buyers to withhold initially and gradually increase

 
### Auction Allocation rule

we can either:
- arrange buy and sell orders by timestamp and match in a first in order
- or we can use a minimum cost maximum flow algorithm (assign a small cost to each edge), to determine the smallest 
  number of matches.
