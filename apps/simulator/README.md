# AU25 Auction Simulator

A comprehensive TypeScript application for simulating **Double-Sided Price-Reversing Clock Auctions** with realistic market dynamics using curve-based participant modeling and interactive visualizations.

## Features

### 🕐 Clock Auction Implementation
- **Price-Reversing Clock Auction**: Full implementation of double-sided price-reversing clock auction rules
- **Curve-Based Participants**: Realistic economic modeling using linear demand/supply curves
- **Dynamic Price Discovery**: Flexible price adjustment with pre/post-reversal mechanisms
- **Quantity-Only Bidding**: Participants submit quantities only, prices announced by auctioneer
- **Allocation Engine**: Timestamp-based allocation for fair matching
- **Constraint Tracking**: Real-time tracking and validation of bidder constraints

### 📊 Visualization Features
- **Interactive Charts**: Built with Plotly.js for rich user interaction
- **Price Timeline Visualization**: Track price discovery across auction rounds
- **Supply & Demand Analysis**: Visualize participant curves and market equilibrium
- **Storybook Integration**: Component documentation and development
- **Responsive Design**: Works across desktop, tablet, and mobile devices

### 🛠️ Technical Features
- **TypeScript Support**: Full type safety and IntelliSense support
- **Extensible Architecture**: Easy to add new participant types and behaviors
- **Comprehensive Examples**: Multiple example scenarios demonstrating features
- **Clean Build Process**: Zero TypeScript compilation errors

## Installation

```bash
cd apps/simulator
pnpm install
```

## Quick Start

### Basic Clock Auction

```typescript
import { 
  ClockAuctionSimulator, 
  ClockAuctionConfig, 
  ClockParticipant,
  createParticipant 
} from '@/types/types';
import { ClockBidGenerator } from '@/simulator/ClockBidGenerator';

// Create participants using curve-based model
const participants: ClockParticipant[] = [
  // Buyer with linear demand curve
  createParticipant(
    'buyer-1',
    'Pension Fund',
    -25,  // maxBuyQuantity (negative for buyers)
    0,    // maxSellQuantity
    2,    // slope (price sensitivity)
    100   // zeroPrice (price at zero quantity)
  ),
  
  // Seller with linear supply curve
  createParticipant(
    'seller-1',
    'Energy Producer',
    0,    // maxBuyQuantity
    25,   // maxSellQuantity (positive for sellers)
    2,    // slope
    80    // zeroPrice
  )
];

// Configure auction
const config: ClockAuctionConfig = {
  id: 'basic-auction',
  name: 'Basic Clock Auction',
  startingPrice: 90,
  preReversalPriceChange: 2,   // Price change before reversal
  postReversalPriceChange: 1,  // Price change after reversal
  roundDuration: 1000,
  participants
};

// Run simulation
const simulator = new ClockAuctionSimulator(config);
const result = await simulator.simulate();

console.log(`Auction completed: ${result.endCondition.type}`);
console.log(`Final price: $${result.endCondition.finalPrice}`);
console.log(`Matched quantity: ${result.endCondition.matchedQuantity}`);
```

## Participant Model

### Curve-Based Participants

Participants are modeled using linear demand/supply curves defined by:

- **maxBuyQuantity**: Maximum quantity willing to buy (negative value)
- **maxSellQuantity**: Maximum quantity willing to sell (positive value)
- **slope**: Price sensitivity (how quantity changes with price)
- **zeroPrice**: Price at which quantity demand/supply is zero

### Demand/Supply Calculation

For any given price `p`, the participant's desired quantity is calculated as:

```
quantity = maxBuyQuantity + (slope * (zeroPrice - p))  // for buyers
quantity = maxSellQuantity + (slope * (p - zeroPrice))  // for sellers
```

### Example Participant Types

```typescript
// Aggressive buyer - high demand at low prices
const aggressiveBuyer = createParticipant(
  'aggressive-buyer',
  'Hedge Fund',
  -50,  // High max buy quantity
  0,
  3,    // High price sensitivity
  120   // High zero price
);

// Conservative seller - limited supply, higher prices
const conservativeSeller = createParticipant(
  'conservative-seller',
  'Small Producer',
  0,
  15,   // Limited max sell quantity
  1.5,  // Lower price sensitivity
  70    // Lower zero price
);

// Balanced trader - can buy or sell
const balancedTrader = createParticipant(
  'balanced-trader',
  'Market Maker',
  -20,  // Can buy up to 20
  20,   // Can sell up to 20
  2,    // Moderate sensitivity
  90    // Market-neutral zero price
);
```

## Auction Configuration

### Core Parameters

```typescript
interface ClockAuctionConfig {
  id: string;                    // Unique auction identifier
  name: string;                  // Human-readable name
  startingPrice: number;         // Initial price announcement
  preReversalPriceChange: number;  // Price increment before reversal
  postReversalPriceChange: number; // Price increment after reversal
  roundDuration: number;         // Duration per round (ms)
  participants: ClockParticipant[]; // Array of participants
}
```

### Price Discovery Mechanism

1. **Pre-Reversal Phase**: Price changes by `preReversalPriceChange` amount
2. **Reversal Detection**: When supply/demand imbalance switches direction
3. **Post-Reversal Phase**: Price changes by `postReversalPriceChange` amount (typically smaller)
4. **Equilibrium**: Auction ends when supply ≈ demand within tolerance

## Examples

### Running the Application

```bash
# Start development server
pnpm dev

# Start Storybook for component development
pnpm storybook

# Build for production
pnpm build

# Run simulator example
pnpm test:simulator
```

### Example Scenarios

The application includes comprehensive examples:

- **src/simulator/examples/clock-auction-example.ts**: Multiple scenarios with different starting prices and increments
- **src/simulator/examples/curve-based-example.ts**: Advanced example with diverse participant types
- **Interactive Storybook**: Component playground with multiple data scenarios

## API Reference

### Core Classes

#### `ClockAuctionSimulator`

Main simulation engine for clock auctions.

```typescript
class ClockAuctionSimulator {
  constructor(config: ClockAuctionConfig)
  async simulate(): Promise<ClockAuctionResult>
}
```

#### `createParticipant`

Utility function for creating curve-based participants.

```typescript
function createParticipant(
  id: string,
  name: string,
  maxBuyQuantity: number,
  maxSellQuantity: number,
  slope: number,
  zeroPrice: number
): ClockParticipant
```

### Result Types

```typescript
interface ClockAuctionResult {
  endCondition: {
    type: 'equilibrium' | 'max_rounds' | 'no_activity';
    finalPrice: number;
    matchedQuantity: number;
  };
  rounds: ClockRound[];
  allocations: Allocation[];
}
```

## Development

### Building

```bash
pnpm build
```

### Testing

```bash
# Run simulator example
pnpm test:simulator

# Start Storybook
pnpm storybook
```

### Project Structure

```
apps/simulator/
├── src/
│   ├── types/           # TypeScript interfaces
│   ├── simulator/       # Auction simulation engines
│   ├── components/      # React visualization components
│   │   ├── charts/     # Chart components (PriceTimelineChart, SupplyDemandChart)
│   │   └── stories/    # Storybook stories
│   ├── hooks/          # Custom React hooks
│   ├── utils/          # Utility functions
│   └── App.tsx         # Main application
├── .storybook/         # Storybook configuration
└── dist/              # Compiled output
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all examples run successfully
6. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Visualization Components

### PriceTimelineChart

Visualizes price evolution across auction rounds.

```typescript
import { PriceTimelineChart } from '@/components/charts/PriceTimelineChart';
import type { PricePoint } from '@/types/types';

const priceHistory: PricePoint[] = [
  { round: 1, price: 10, timestamp: new Date() },
  { round: 2, price: 15, timestamp: new Date() },
  // ... more data points
];

function MyComponent() {
  return (
    <PriceTimelineChart 
      priceHistory={priceHistory}
      title="Auction Price Discovery"
      width={800}
      height={400}
    />
  );
}
```

### SupplyDemandChart

Visualizes participant curves and market equilibrium.

```typescript
import { SupplyDemandChart } from '@/components/charts/SupplyDemandChart';

// Component automatically integrates with simulator data
<SupplyDemandChart 
  data={supplyDemandData}
  title="Market Analysis"
/>
```

## Storybook

Explore components interactively:

```bash
pnpm storybook
```

Storybook provides:
- Interactive component playground
- Documentation with examples
- Control panels for props
- Multiple data scenarios
- Responsive testing

## Changelog

See [CHANGELOG.md](../../docs/CHANGELOG.md) for version history and updates.