import React from 'react';
import Plot from 'react-plotly.js';
import type { PricePoint } from '@/types/types';

interface PriceTimelineChartProps {
  priceHistory: PricePoint[];
  title?: string;
  width?: number;
  height?: number;
}

export const PriceTimelineChart: React.FC<PriceTimelineChartProps> = ({
  priceHistory,
  title = 'Price Discovery Timeline',
  width = 800,
  height = 400
}) => {
  const data = [
    {
      x: priceHistory.map(p => p.roundNumber),
      y: priceHistory.map(p => p.price),
      type: 'scatter' as const,
      mode: 'lines+markers' as const,
      name: 'Price',
      line: {
        color: '#2563eb',
        width: 3
      },
      marker: {
        color: '#2563eb',
        size: 8
      }
    }
  ];

  const layout = {
    title: {
      text: title,
      font: { size: 18, color: '#1f2937' }
    },
    xaxis: {
      title: 'Round',
      gridcolor: '#e5e7eb',
      tickfont: { color: '#6b7280' }
    },
    yaxis: {
      title: 'Price',
      gridcolor: '#e5e7eb',
      tickfont: { color: '#6b7280' }
    },
    plot_bgcolor: '#ffffff',
    paper_bgcolor: '#ffffff',
    margin: { t: 60, r: 40, b: 60, l: 60 },
    width,
    height
  };

  const config = {
    displayModeBar: true,
    displaylogo: false,
    modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d'],
    responsive: true
  };

  return (
    <div className="w-full">
      <Plot
        data={data}
        layout={layout as any}
        config={config as any}
        className="w-full"
      />
    </div>
  );
};

export default PriceTimelineChart;