import type { Meta, StoryObj } from '@storybook/react';
import { PriceTimelineChart } from './PriceTimelineChart';

const meta: Meta<typeof PriceTimelineChart> = {
  title: 'Charts/PriceTimelineChart',
  component: PriceTimelineChart,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A chart component that visualizes price discovery over auction rounds using Plotly.js'
      }
    }
  },
  tags: ['autodocs'],
  argTypes: {
    title: {
      control: 'text',
      description: 'Chart title'
    },
    width: {
      control: { type: 'range', min: 400, max: 1200, step: 50 },
      description: 'Chart width in pixels'
    },
    height: {
      control: { type: 'range', min: 200, max: 800, step: 50 },
      description: 'Chart height in pixels'
    }
  }
};

export default meta;
type Story = StoryObj<typeof meta>;

// Sample data for stories
const samplePriceHistory = [
  { round: 1, price: 10, timestamp: new Date('2024-01-01T10:00:00Z') },
  { round: 2, price: 15, timestamp: new Date('2024-01-01T10:01:00Z') },
  { round: 3, price: 22, timestamp: new Date('2024-01-01T10:02:00Z') },
  { round: 4, price: 28, timestamp: new Date('2024-01-01T10:03:00Z') },
  { round: 5, price: 35, timestamp: new Date('2024-01-01T10:04:00Z') },
  { round: 6, price: 40, timestamp: new Date('2024-01-01T10:05:00Z') },
  { round: 7, price: 42, timestamp: new Date('2024-01-01T10:06:00Z') },
  { round: 8, price: 43, timestamp: new Date('2024-01-01T10:07:00Z') }
];

const volatilePriceHistory = [
  { round: 1, price: 10, timestamp: new Date('2024-01-01T10:00:00Z') },
  { round: 2, price: 25, timestamp: new Date('2024-01-01T10:01:00Z') },
  { round: 3, price: 18, timestamp: new Date('2024-01-01T10:02:00Z') },
  { round: 4, price: 35, timestamp: new Date('2024-01-01T10:03:00Z') },
  { round: 5, price: 30, timestamp: new Date('2024-01-01T10:04:00Z') },
  { round: 6, price: 45, timestamp: new Date('2024-01-01T10:05:00Z') },
  { round: 7, price: 42, timestamp: new Date('2024-01-01T10:06:00Z') },
  { round: 8, price: 48, timestamp: new Date('2024-01-01T10:07:00Z') },
  { round: 9, price: 46, timestamp: new Date('2024-01-01T10:08:00Z') },
  { round: 10, price: 50, timestamp: new Date('2024-01-01T10:09:00Z') }
];

const longAuctionHistory = Array.from({ length: 20 }, (_, i) => ({
  round: i + 1,
  price: 10 + Math.log(i + 1) * 8 + Math.random() * 3,
  timestamp: new Date(`2024-01-01T${10 + Math.floor(i / 60)}:${(i % 60).toString().padStart(2, '0')}:00Z`)
}));

export const Default: Story = {
  args: {
    priceHistory: samplePriceHistory,
    title: 'Price Discovery Timeline',
    width: 800,
    height: 400
  }
};

export const VolatileMarket: Story = {
  args: {
    priceHistory: volatilePriceHistory,
    title: 'Volatile Market Conditions',
    width: 800,
    height: 400
  }
};

export const LongAuction: Story = {
  args: {
    priceHistory: longAuctionHistory,
    title: 'Extended Auction (20 Rounds)',
    width: 1000,
    height: 500
  }
};

export const Compact: Story = {
  args: {
    priceHistory: samplePriceHistory,
    title: 'Compact View',
    width: 500,
    height: 300
  }
};

export const CustomTitle: Story = {
  args: {
    priceHistory: samplePriceHistory,
    title: 'AU25 Clock Auction - Energy Spectrum Rights',
    width: 800,
    height: 400
  }
};