import type { Meta, StoryObj } from '@storybook/react';
import { PriceTimelineChart } from './PriceTimelineChart';

const meta: Meta<typeof PriceTimelineChart> = {
  title: 'Charts/PriceTimelineChart',
  component: PriceTimelineChart,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A chart component that visualizes price discovery over auction rounds using Plotly.js'
      }
    }
  },
  tags: ['autodocs'],
  argTypes: {
    title: {
      control: 'text',
      description: 'Chart title'
    },
    width: {
      control: { type: 'range', min: 400, max: 1200, step: 50 },
      description: 'Chart width in pixels'
    },
    height: {
      control: { type: 'range', min: 200, max: 800, step: 50 },
      description: 'Chart height in pixels'
    }
  }
};

export default meta;
type Story = StoryObj<typeof meta>;

// Sample data for stories
const samplePriceHistory = [
  { roundNumber: 1, price: 10, buyVolume: 100, sellVolume: 80, direction: 'up' as const },
  { roundNumber: 2, price: 15, buyVolume: 90, sellVolume: 85, direction: 'up' as const },
  { roundNumber: 3, price: 22, buyVolume: 85, sellVolume: 90, direction: 'up' as const },
  { roundNumber: 4, price: 28, buyVolume: 80, sellVolume: 95, direction: 'up' as const },
  { roundNumber: 5, price: 35, buyVolume: 75, sellVolume: 100, direction: 'up' as const },
  { roundNumber: 6, price: 40, buyVolume: 70, sellVolume: 105, direction: 'up' as const },
  { roundNumber: 7, price: 42, buyVolume: 65, sellVolume: 110, direction: 'up' as const },
  { roundNumber: 8, price: 43, buyVolume: 60, sellVolume: 115, direction: 'up' as const }
];

const volatilePriceHistory = [
  { roundNumber: 1, price: 10, buyVolume: 100, sellVolume: 80, direction: 'up' as const },
  { roundNumber: 2, price: 25, buyVolume: 120, sellVolume: 60, direction: 'up' as const },
  { roundNumber: 3, price: 18, buyVolume: 80, sellVolume: 120, direction: 'down' as const },
  { roundNumber: 4, price: 35, buyVolume: 140, sellVolume: 50, direction: 'up' as const },
  { roundNumber: 5, price: 30, buyVolume: 90, sellVolume: 110, direction: 'down' as const },
  { roundNumber: 6, price: 45, buyVolume: 150, sellVolume: 40, direction: 'up' as const },
  { roundNumber: 7, price: 42, buyVolume: 95, sellVolume: 105, direction: 'down' as const },
  { roundNumber: 8, price: 48, buyVolume: 130, sellVolume: 70, direction: 'up' as const },
  { roundNumber: 9, price: 46, buyVolume: 100, sellVolume: 100, direction: 'down' as const },
  { roundNumber: 10, price: 50, buyVolume: 110, sellVolume: 90, direction: 'up' as const }
];

const longAuctionHistory = Array.from({ length: 20 }, (_, i) => ({
  roundNumber: i + 1,
  price: 10 + Math.log(i + 1) * 8 + Math.random() * 3,
  buyVolume: 100 - i * 2 + Math.random() * 20,
  sellVolume: 80 + i * 3 + Math.random() * 15,
  direction: (i % 3 === 0 ? 'down' : 'up') as const
}));

export const Default: Story = {
  args: {
    priceHistory: samplePriceHistory,
    title: 'Price Discovery Timeline',
    width: 800,
    height: 400
  }
};

export const VolatileMarket: Story = {
  args: {
    priceHistory: volatilePriceHistory,
    title: 'Volatile Market Conditions',
    width: 800,
    height: 400
  }
};

export const LongAuction: Story = {
  args: {
    priceHistory: longAuctionHistory,
    title: 'Extended Auction (20 Rounds)',
    width: 1000,
    height: 500
  }
};

export const Compact: Story = {
  args: {
    priceHistory: samplePriceHistory,
    title: 'Compact View',
    width: 500,
    height: 300
  }
};

export const CustomTitle: Story = {
  args: {
    priceHistory: samplePriceHistory,
    title: 'AU25 Clock Auction - Energy Spectrum Rights',
    width: 800,
    height: 400
  }
};