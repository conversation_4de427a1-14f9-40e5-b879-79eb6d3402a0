import type { Meta, StoryObj } from '@storybook/react';
import { SupplyDemandChart, SupplyDemandPoint } from './SupplyDemandChart';

const meta: Meta<typeof SupplyDemandChart> = {
  component: SupplyDemandChart,
  title: 'Charts/SupplyDemandChart',
  parameters: {
    layout: 'centered',
  },
};

export default meta;

type Story = StoryObj<typeof SupplyDemandChart>;

// Sample data generators
const generateSupplyCurve = (basePrice: number = 10, slope: number = 0.5, points: number = 10): SupplyDemandPoint[] => {
  return Array.from({ length: points }, (_, i) => ({
    price: basePrice + (i * slope),
    quantity: i * 100 + 50,
    type: 'supply' as const
  }));
};

const generateDemandCurve = (basePrice: number = 20, slope: number = -0.3, points: number = 10): SupplyDemandPoint[] => {
  return Array.from({ length: points }, (_, i) => ({
    price: basePrice + (i * slope),
    quantity: i * 100 + 50,
    type: 'demand' as const
  }));
};

// Default story with balanced market
export const Default: Story = {
  args: {
    data: [
      ...generateSupplyCurve(8, 0.4, 12),
      ...generateDemandCurve(18, -0.3, 12)
    ],
    title: 'Market Supply and Demand',
    showEquilibrium: true,
    config: {
      width: 800,
      height: 500,
      showGrid: true,
      showLegend: true
    }
  }
};

// Elastic supply scenario
export const ElasticSupply: Story = {
  args: {
    data: [
      ...generateSupplyCurve(5, 0.8, 15), // More elastic (steeper slope)
      ...generateDemandCurve(20, -0.2, 15)
    ],
    title: 'Elastic Supply Market',
    showEquilibrium: true,
    config: {
      width: 800,
      height: 500
    }
  }
};

// Inelastic demand scenario
export const InelasticDemand: Story = {
  args: {
    data: [
      ...generateSupplyCurve(10, 0.3, 12),
      ...generateDemandCurve(25, -0.1, 12) // Less elastic (flatter slope)
    ],
    title: 'Inelastic Demand Market',
    showEquilibrium: true,
    equilibriumColor: '#F59E0B',
    config: {
      width: 800,
      height: 500
    }
  }
};

// Volatile market with multiple intersection points
export const VolatileMarket: Story = {
  args: {
    data: [
      // Supply with some volatility
      { price: 5, quantity: 100, type: 'supply' },
      { price: 8, quantity: 200, type: 'supply' },
      { price: 12, quantity: 250, type: 'supply' },
      { price: 15, quantity: 400, type: 'supply' },
      { price: 18, quantity: 600, type: 'supply' },
      { price: 22, quantity: 900, type: 'supply' },
      // Demand with volatility
      { price: 25, quantity: 50, type: 'demand' },
      { price: 20, quantity: 150, type: 'demand' },
      { price: 16, quantity: 300, type: 'demand' },
      { price: 12, quantity: 500, type: 'demand' },
      { price: 8, quantity: 750, type: 'demand' },
      { price: 5, quantity: 1000, type: 'demand' }
    ],
    title: 'Volatile Market Conditions',
    showEquilibrium: true,
    equilibriumColor: '#EF4444',
    config: {
      width: 800,
      height: 500
    }
  }
};

// No equilibrium scenario
export const NoEquilibrium: Story = {
  args: {
    data: [
      // Supply curve that doesn't intersect with demand
      ...generateSupplyCurve(20, 0.5, 8),
      ...generateDemandCurve(15, -0.2, 8)
    ],
    title: 'Market with No Clear Equilibrium',
    showEquilibrium: true,
    config: {
      width: 800,
      height: 500
    }
  }
};

// Early auction round with limited data
export const EarlyRound: Story = {
  args: {
    data: [
      // Limited supply data
      { price: 10, quantity: 100, type: 'supply' },
      { price: 12, quantity: 150, type: 'supply' },
      { price: 15, quantity: 200, type: 'supply' },
      // Limited demand data
      { price: 18, quantity: 80, type: 'demand' },
      { price: 14, quantity: 120, type: 'demand' },
      { price: 11, quantity: 180, type: 'demand' }
    ],
    title: 'Early Auction Round - Limited Data',
    showEquilibrium: true,
    config: {
      width: 800,
      height: 500,
      showGrid: true
    }
  }
};

// Custom styling example
export const CustomStyling: Story = {
  args: {
    data: [
      ...generateSupplyCurve(6, 0.6, 10),
      ...generateDemandCurve(22, -0.4, 10)
    ],
    title: 'Custom Styled Market Analysis',
    showEquilibrium: true,
    equilibriumColor: '#8B5CF6',
    colorScheme: {
      primary: '#059669', // Green for supply
      secondary: '#DC2626', // Red for demand
      success: '#8B5CF6',
      warning: '#F59E0B',
      error: '#EF4444',
      neutral: '#6B7280',
      participants: ['#059669', '#DC2626', '#8B5CF6', '#F59E0B']
    },
    config: {
      width: 800,
      height: 500,
      showGrid: false,
      showLegend: true
    }
  }
};

// Interactive example
export const Interactive: Story = {
  args: {
    data: [
      ...generateSupplyCurve(7, 0.5, 12),
      ...generateDemandCurve(19, -0.35, 12)
    ],
    title: 'Interactive Market Chart',
    showEquilibrium: true,
    config: {
      width: 800,
      height: 500,
      responsive: true
    },
    onDataPointClick: (dataPoint: any) => {
      console.log('Data point clicked:', dataPoint);
      alert(`Clicked: ${dataPoint.type} at Price: $${dataPoint.price?.toFixed(2)}, Quantity: ${dataPoint.quantity}`);
    }
  }
};