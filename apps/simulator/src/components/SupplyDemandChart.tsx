import React, { useMemo } from 'react';
import Plot from 'react-plotly.js';
import type { ChartProps } from '@/types/types';

// Inline the expected shape for supply/demand points
export interface SupplyDemandPoint {
  price: number;
  quantity: number;
  type: 'supply' | 'demand';
}

interface SupplyDemandChartProps extends ChartProps<SupplyDemandPoint[]> {
  title?: string;
  showEquilibrium?: boolean;
  equilibriumColor?: string;
}

interface EquilibriumPoint {
  price: number;
  quantity: number;
}

export const SupplyDemandChart: React.FC<SupplyDemandChartProps> = ({
  data,
  config = {},
  colorScheme = {
    primary: '#3B82F6',
    secondary: '#EF4444',
    success: '#10B981',
    warning: '#F59E0B',
    error: '#EF4444',
    neutral: '#6B7280',
    participants: ['#3B82F6', '#EF4444', '#10B981', '#F59E0B']
  },
  title = 'Supply and Demand Analysis',
  showEquilibrium = true,
  equilibriumColor = '#10B981',
  onDataPointClick,
  onExport: _onExport
}) => {
  const { supplyData, demandData, equilibrium } = useMemo(() => {
    const supply = data.filter(point => point.type === 'supply').sort((a, b) => a.price - b.price);
    const demand = data.filter(point => point.type === 'demand').sort((a, b) => a.price - b.price);
    
    // Calculate equilibrium point
    let equilibriumPoint: EquilibriumPoint | null = null;
    
    if (supply.length > 0 && demand.length > 0 && showEquilibrium) {
      // Find intersection point using linear interpolation
      for (let i = 0; i < supply.length - 1; i++) {
        for (let j = 0; j < demand.length - 1; j++) {
          const s1 = supply[i];
          const s2 = supply[i + 1];
          const d1 = demand[j];
          const d2 = demand[j + 1];
          
          // Check if lines intersect
          if (s1.price <= d1.price && s2.price >= d2.price) {
            // Linear interpolation to find intersection
            const priceRange = Math.max(s2.price, d2.price) - Math.min(s1.price, d1.price);
            if (priceRange > 0) {
              const supplySlope = (s2.quantity - s1.quantity) / (s2.price - s1.price);
              const demandSlope = (d2.quantity - d1.quantity) / (d2.price - d1.price);
              
              if (Math.abs(supplySlope - demandSlope) > 0.001) {
                const intersectionPrice = (d1.quantity - s1.quantity + supplySlope * s1.price - demandSlope * d1.price) / (supplySlope - demandSlope);
                const intersectionQuantity = s1.quantity + supplySlope * (intersectionPrice - s1.price);
                
                if (intersectionPrice >= Math.min(s1.price, d1.price) && 
                    intersectionPrice <= Math.max(s2.price, d2.price) &&
                    intersectionQuantity >= 0) {
                  equilibriumPoint = {
                    price: intersectionPrice,
                    quantity: intersectionQuantity
                  };
                  break;
                }
              }
            }
          }
        }
        if (equilibriumPoint) break;
      }
    }
    
    return {
      supplyData: supply,
      demandData: demand,
      equilibrium: equilibriumPoint
    };
  }, [data, showEquilibrium]);

  const plotData = useMemo(() => {
    const traces: any[] = [];
    
    // Supply curve
    if (supplyData.length > 0) {
      traces.push({
        x: supplyData.map(point => point.quantity),
        y: supplyData.map(point => point.price),
        type: 'scatter',
        mode: 'lines+markers',
        name: 'Supply',
        line: {
          color: colorScheme.primary,
          width: 3
        },
        marker: {
          color: colorScheme.primary,
          size: 6
        },
        hovertemplate: '<b>Supply</b><br>' +
                      'Price: $%{y:.2f}<br>' +
                      'Quantity: %{x}<br>' +
                      '<extra></extra>'
      });
    }
    
    // Demand curve
    if (demandData.length > 0) {
      traces.push({
        x: demandData.map(point => point.quantity),
        y: demandData.map(point => point.price),
        type: 'scatter',
        mode: 'lines+markers',
        name: 'Demand',
        line: {
          color: colorScheme.secondary,
          width: 3
        },
        marker: {
          color: colorScheme.secondary,
          size: 6
        },
        hovertemplate: '<b>Demand</b><br>' +
                      'Price: $%{y:.2f}<br>' +
                      'Quantity: %{x}<br>' +
                      '<extra></extra>'
      });
    }
    
    // Equilibrium point
    if (equilibrium && showEquilibrium) {
      traces.push({
        x: [equilibrium.quantity],
        y: [equilibrium.price],
        type: 'scatter',
        mode: 'markers',
        name: 'Equilibrium',
        marker: {
          color: equilibriumColor,
          size: 12,
          symbol: 'star',
          line: {
            color: 'white',
            width: 2
          }
        },
        hovertemplate: '<b>Market Equilibrium</b><br>' +
                      'Price: $%{y:.2f}<br>' +
                      'Quantity: %{x}<br>' +
                      '<extra></extra>'
      });
    }
    
    return traces;
  }, [supplyData, demandData, equilibrium, colorScheme, showEquilibrium, equilibriumColor]);

  const layout = {
    title: {
      text: title,
      font: { size: 18, family: 'Inter, sans-serif' }
    },
    xaxis: {
      title: 'Quantity',
      gridcolor: config.showGrid !== false ? '#E5E7EB' : 'transparent',
      zeroline: false
    },
    yaxis: {
      title: 'Price ($)',
      gridcolor: config.showGrid !== false ? '#E5E7EB' : 'transparent',
      zeroline: false
    },
    showlegend: config.showLegend !== false,
    legend: {
      orientation: 'h',
      y: -0.2,
      x: 0.5,
      xanchor: 'center'
    },
    margin: { t: 60, r: 40, b: 80, l: 60 },
    plot_bgcolor: 'white',
    paper_bgcolor: 'white',
    font: { family: 'Inter, sans-serif' },
    hovermode: 'closest'
  };

  const plotConfig = {
    responsive: config.responsive !== false,
    displayModeBar: true,
    modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d'],
    displaylogo: false,
    toImageButtonOptions: {
      format: 'png',
      filename: 'supply-demand-chart',
      height: config.height || 500,
      width: config.width || 800,
      scale: 2
    }
  };

  const handlePlotClick = (event: any) => {
    if (onDataPointClick && event.points && event.points.length > 0) {
      const point = event.points[0];
      onDataPointClick({
        price: point.y,
        quantity: point.x,
        type: point.data.name?.toLowerCase(),
        curveIndex: point.curveNumber
      });
    }
  };

  return (
    <div className="w-full">
      <Plot
        data={plotData}
        layout={{
          ...layout,
          width: config.width || undefined,
          height: config.height || 500
        } as any}
        config={plotConfig as any}
        onClick={handlePlotClick}
        className="w-full"
      />
      
      {/* Market Analysis Panel */}
      {equilibrium && (
        <div className="mt-4 p-4 bg-gray-50 rounded-lg">
          <h3 className="text-lg font-semibold text-gray-800 mb-2">Market Analysis</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                ${equilibrium.price.toFixed(2)}
              </div>
              <div className="text-sm text-gray-600">Equilibrium Price</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {equilibrium.quantity.toLocaleString()}
              </div>
              <div className="text-sm text-gray-600">Equilibrium Quantity</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                ${(equilibrium.price * equilibrium.quantity).toLocaleString()}
              </div>
              <div className="text-sm text-gray-600">Market Value</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SupplyDemandChart;