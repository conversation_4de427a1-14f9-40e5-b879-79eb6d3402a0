import React, { useState } from 'react';
import './App.css';

function App() {
  console.log('App component rendering...');

  const [simulationResult, setSimulationResult] = useState<string>('Not run yet');
  const [isRunning, setIsRunning] = useState(false);

  const runSimulation = async () => {
    try {
      setIsRunning(true);
      setSimulationResult('Running...');

      // Simulate some work
      await new Promise(resolve => setTimeout(resolve, 1000));

      setSimulationResult('Test simulation completed successfully!');

    } catch (error) {
      console.error('Simulation error:', error);
      setSimulationResult(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsRunning(false);
    }
  };

  return (
    <div className="App">
      <h1 className="text-2xl font-bold mb-4">Auction Simulator Demo</h1>
      <p>React app is working!</p>

      <div className="mt-6">
        <button
          onClick={runSimulation}
          disabled={isRunning}
          className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
        >
          {isRunning ? 'Running Simulation...' : 'Run Simulation'}
        </button>

        <div className="mt-4 p-4 border rounded">
          <h3 className="font-semibold">Simulation Result:</h3>
          <p>{simulationResult}</p>
        </div>
      </div>
    </div>
  );
}



export default App;
