import React, { useMemo } from 'react';
import { PriceTimelineChart } from './components/PriceTimelineChart';
import { SupplyDemandChart, SupplyDemandPoint } from './components/SupplyDemandChart';
import { ClockAuctionSimulator } from './simulator';
import type { PricePoint } from '@/types/types';
import './App.css';

// Example: set up a simple auction config
const auctionConfig = {
  id: 'demo-auction',
  name: 'Demo Auction',
  startingPrice: 10,
  preReversalPriceChange: 2,
  postReversalPriceChange: 1,
  roundDuration: 1000,
  participants: [
    {
      id: 'buyer1',
      name: 'Buyer 1',
      curveParams: { maxBuyQuantity: -100, maxSellQuantity: 0, slope: -2, zeroPrice: 50 },
      quantityFunction: (price: number) => Math.max(0, 100 - 2 * price)
    },
    {
      id: 'seller1',
      name: 'Seller 1',
      curveParams: { maxBuyQuantity: 0, maxSellQuantity: 100, slope: 2, zeroPrice: 10 },
      quantityFunction: (price: number) => Math.max(0, 2 * price - 10)
    }
  ]
};

const simulator = new ClockAuctionSimulator(auctionConfig);
const result = simulator.simulate();

const priceHistory: PricePoint[] = result.priceHistory;

// Generate supply/demand points for the chart from participant curves
const sampleSupplyDemandData: SupplyDemandPoint[] = useMemo(() => {
  const prices = Array.from({ length: 20 }, (_, i) => 5 + i * 2);
  const supply: SupplyDemandPoint[] = prices.map(price => ({
    price,
    quantity: auctionConfig.participants[1].quantityFunction(price),
    type: 'supply'
  }));
  const demand: SupplyDemandPoint[] = prices.map(price => ({
    price,
    quantity: auctionConfig.participants[0].quantityFunction(price),
    type: 'demand'
  }));
  return [...supply, ...demand];
}, []);

function App() {
  return (
    <div className="App">
      <h1 className="text-2xl font-bold mb-4">Auction Simulator Demo</h1>
      <PriceTimelineChart priceHistory={priceHistory} />
      <SupplyDemandChart data={sampleSupplyDemandData} />
    </div>
  );
}

export default App;
