import React, { useState } from 'react';
import './App.css';

function App() {
  console.log('App component rendering...');

  const [simulationResult, setSimulationResult] = useState<string>('Not run yet');
  const [isRunning, setIsRunning] = useState(false);

  const testImports = async () => {
    try {
      setIsRunning(true);
      setSimulationResult('Testing imports...');

      console.log('Testing dynamic import...');
      const simulatorModule = await import('./simulator');
      console.log('Simulator module loaded:', Object.keys(simulatorModule));

      const { ClockAuctionSimulator, createParticipant } = simulatorModule;
      console.log('ClockAuctionSimulator:', typeof ClockAuctionSimulator);
      console.log('createParticipant:', typeof createParticipant);

      setSimulationResult(`Import test successful! Found: ${Object.keys(simulatorModule).join(', ')}`);

    } catch (error) {
      console.error('Import error:', error);
      setSimulationResult(`Import error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsRunning(false);
    }
  };

  return (
    <div className="App">
      <h1 className="text-2xl font-bold mb-4">Auction Simulator Demo</h1>
      <p>React app is working!</p>

      <div className="mt-6">
        <button
          onClick={testImports}
          disabled={isRunning}
          className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
        >
          {isRunning ? 'Testing Imports...' : 'Test Imports'}
        </button>

        <div className="mt-4 p-4 border rounded">
          <h3 className="font-semibold">Simulation Result:</h3>
          <p>{simulationResult}</p>
        </div>
      </div>
    </div>
  );
}



export default App;
