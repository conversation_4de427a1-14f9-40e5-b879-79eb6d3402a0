// Clock Auction Specific Types

/**
 * Quantity-only bid for clock auction (no price - price is announced by auctioneer)
 */
export interface ClockBid {
  id: string;
  participantId: string;
  side: 'buy' | 'sell';
  quantity: number;
  timestamp: Date;
  roundNumber: number;
}

/**
 * Bidder constraints that enforce rationality rules
 */
export interface BidderConstraints {
  participantId: string;
  maxBuy: number;     // Maximum quantity they can bid to buy
  minBuy: number;     // Minimum quantity they must bid to buy (if bidding to buy)
  maxSell: number;    // Maximum quantity they can bid to sell
  minSell: number;    // Minimum quantity they must bid to sell (if bidding to sell)
}

/**
 * Linear demand/supply curve parameters
 */
export interface LinearCurveParams {
  maxBuyQuantity: number;    // Maximum quantity to buy (negative quantity)
  maxSellQuantity: number;   // Maximum quantity to sell (positive quantity)
  slope: number;             // Quantity/price slope of the linear curve
  zeroPrice: number;         // Price at which the curve crosses the x-axis (quantity = 0)
}

/**
 * Quantity function type - takes price and returns quantity
 */
export type QuantityFunction = (price: number) => number;

/**
 * Clock auction participant with linear demand/supply curve
 */
export interface ClockParticipant {
  id: string;
  name: string;
  curveParams: LinearCurveParams;
  quantityFunction: QuantityFunction;  // Generated from curveParams
}

/**
 * Clock auction round with price announcement
 */
export interface ClockAuctionRound {
  id: string;
  roundNumber: number;
  announcedPrice: number;  // Price announced by auctioneer
  startTime: Date;
  endTime: Date;
  status: 'pending' | 'active' | 'completed';
  bids: ClockBid[];
  totalBuyVolume: number;
  totalSellVolume: number;
  constraints: BidderConstraints[];  // Constraints for each participant
}

/**
 * Clock auction configuration
 */
export interface ClockAuctionConfig {
  id: string;
  name: string;
  startingPrice: number;
  preReversalPriceChange: number;  // Price increment before reversal
  postReversalPriceChange: number; // Price increment after reversal (smaller)
  roundDuration: number;           // Time limit per round in milliseconds
  participants: ClockParticipant[];
}

// ClockBiddingStrategy removed - participants now use linear demand/supply curves

/**
 * Price movement direction
 */
export type PriceDirection = 'up' | 'down' | 'stable';

/**
 * Auction ending condition
 */
export interface AuctionEndCondition {
  type: 'equilibrium' | 'overshoot';
  roundNumber: number;
  finalPrice: number;
  matchedQuantity: number;
}

/**
 * Clock auction simulation result
 */
export interface ClockAuctionResult {
  auctionId: string;
  rounds: ClockAuctionRound[];
  endCondition: AuctionEndCondition;
  allocationRound: number;  // Round used for final allocation
  allocations: Allocation[];
  priceHistory: PricePoint[];
  duration: number;
}

/**
 * Final allocation for a participant
 */
export interface Allocation {
  participantId: string;
  side: 'buy' | 'sell';
  quantity: number;
  price: number;  // Uniform clearing price
}

/**
 * Price point in auction history
 */
export interface PricePoint {
  roundNumber: number;
  price: number;
  buyVolume: number;
  sellVolume: number;
  direction: PriceDirection;
}

// Legacy types removed - clock auction uses ClockBid, ClockParticipant, etc.
// All price-based bidding has been replaced with quantity-only bidding

export interface MarketData {
  timestamp: Date;
  price: number;
  volume: number;
  spread: number;
  volatility: number;
}

export interface SimulationResult {
  auctionId: string;
  rounds: ClockAuctionRound[];
  finalPrice: number;
  totalVolume: number;
  participantResults: ParticipantResult[];
  marketData: MarketData[];
  duration: number;
}

export interface ParticipantResult {
  participantId: string;
  totalBids: number;
  successfulBids: number;
  totalVolume: number;
  averagePrice: number;
  profit: number;
}