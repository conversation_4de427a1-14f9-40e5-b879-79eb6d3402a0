import { describe, it, expect, beforeEach } from 'vitest';
import { ClockAuctionSimulator } from '../simulator/ClockAuctionSimulator';
import { createParticipant } from '../utils/curveUtils';
import type { ClockAuctionConfig } from '../../types/types';

describe('ClockAuctionSimulator', () => {
  let basicConfig: ClockAuctionConfig;

  beforeEach(() => {
    // Create a basic auction configuration for testing
    basicConfig = {
      id: 'test-auction',
      name: 'Test Auction',
      startingPrice: 30,
      preReversalPriceChange: 5,
      postReversalPriceChange: 1,
      roundDuration: 1000,
      participants: [
        // Buyer: willing to buy more at lower prices
        createParticipant('buyer1', 'Test Buyer', -100, 0, -2, 50),
        // Seller: willing to sell more at higher prices  
        createParticipant('seller1', 'Test Seller', 0, 100, 2, 20)
      ]
    };
  });

  describe('Basic Auction Mechanics', () => {
    it('should create a simulator with valid config', () => {
      const simulator = new ClockAuctionSimulator(basicConfig);
      expect(simulator).toBeDefined();
    });

    it('should run a complete auction simulation', async () => {
      const simulator = new ClockAuctionSimulator(basicConfig);
      const result = await simulator.simulate();

      expect(result).toBeDefined();
      expect(result.auctionId).toBe('test-auction');
      expect(result.rounds.length).toBeGreaterThan(0);
      expect(result.endCondition).toBeDefined();
      expect(result.priceHistory.length).toBeGreaterThan(0);
      expect(result.duration).toBeGreaterThan(0);
    });

    it('should generate price history with correct structure', async () => {
      const simulator = new ClockAuctionSimulator(basicConfig);
      const result = await simulator.simulate();

      expect(result.priceHistory.length).toBeGreaterThan(0);
      
      result.priceHistory.forEach(point => {
        expect(point).toHaveProperty('roundNumber');
        expect(point).toHaveProperty('price');
        expect(point).toHaveProperty('buyVolume');
        expect(point).toHaveProperty('sellVolume');
        expect(point).toHaveProperty('direction');
        expect(typeof point.roundNumber).toBe('number');
        expect(typeof point.price).toBe('number');
        expect(typeof point.buyVolume).toBe('number');
        expect(typeof point.sellVolume).toBe('number');
        expect(['up', 'down', 'stable']).toContain(point.direction);
      });
    });
  });

  describe('Price Discovery Mechanism', () => {
    it('should start with the configured starting price', async () => {
      const simulator = new ClockAuctionSimulator(basicConfig);
      const result = await simulator.simulate();

      expect(result.rounds[0].announcedPrice).toBe(30);
      expect(result.priceHistory[0].price).toBe(30);
    });

    it('should use pre-reversal price changes initially', async () => {
      const simulator = new ClockAuctionSimulator(basicConfig);
      const result = await simulator.simulate();

      // Check that early price changes use the pre-reversal increment
      if (result.rounds.length >= 2) {
        const priceDiff = Math.abs(result.rounds[1].announcedPrice - result.rounds[0].announcedPrice);
        expect(priceDiff).toBe(5); // preReversalPriceChange
      }
    });

    it('should increase price when demand > supply', async () => {
      // Create config where buyer has high demand at starting price
      const demandExcessConfig = {
        ...basicConfig,
        participants: [
          createParticipant('aggressive-buyer', 'Aggressive Buyer', -200, 0, -1, 60),
          createParticipant('limited-seller', 'Limited Seller', 0, 50, 3, 25)
        ]
      };

      const simulator = new ClockAuctionSimulator(demandExcessConfig);
      const result = await simulator.simulate();

      // First round should have demand > supply, so price should increase
      const firstRound = result.rounds[0];
      expect(firstRound.totalBuyVolume).toBeGreaterThan(firstRound.totalSellVolume);
      
      if (result.rounds.length >= 2) {
        expect(result.rounds[1].announcedPrice).toBeGreaterThan(result.rounds[0].announcedPrice);
      }
    });
  });

  describe('Auction Ending Conditions', () => {
    it('should end with a valid end condition', async () => {
      const simulator = new ClockAuctionSimulator(basicConfig);
      const result = await simulator.simulate();

      expect(['equilibrium', 'overshoot']).toContain(result.endCondition.type);
      expect(typeof result.endCondition.finalPrice).toBe('number');
      expect(typeof result.endCondition.matchedQuantity).toBe('number');
      expect(result.endCondition.roundNumber).toBeGreaterThan(0);
    });

    it('should determine allocation round correctly', async () => {
      const simulator = new ClockAuctionSimulator(basicConfig);
      const result = await simulator.simulate();

      expect(result.allocationRound).toBeGreaterThan(0);
      expect(result.allocationRound).toBeLessThanOrEqual(result.rounds.length);
      
      // Allocation round should exist in the rounds
      const allocationRoundData = result.rounds.find(r => r.roundNumber === result.allocationRound);
      expect(allocationRoundData).toBeDefined();
    });
  });

  describe('Participant Behavior', () => {
    it('should generate bids for all participants in each round', async () => {
      const simulator = new ClockAuctionSimulator(basicConfig);
      const result = await simulator.simulate();

      result.rounds.forEach(round => {
        // Should have bids from both participants (buyer and seller)
        expect(round.bids.length).toBeGreaterThanOrEqual(0);
        
        // Check bid structure
        round.bids.forEach(bid => {
          expect(bid).toHaveProperty('id');
          expect(bid).toHaveProperty('participantId');
          expect(bid).toHaveProperty('side');
          expect(bid).toHaveProperty('quantity');
          expect(bid).toHaveProperty('timestamp');
          expect(bid).toHaveProperty('roundNumber');
          expect(['buy', 'sell']).toContain(bid.side);
          expect(typeof bid.quantity).toBe('number');
          expect(bid.quantity).toBeGreaterThanOrEqual(0);
        });
      });
    });

    it('should calculate total volumes correctly', async () => {
      const simulator = new ClockAuctionSimulator(basicConfig);
      const result = await simulator.simulate();

      result.rounds.forEach(round => {
        const buyBids = round.bids.filter(bid => bid.side === 'buy');
        const sellBids = round.bids.filter(bid => bid.side === 'sell');
        
        const calculatedBuyVolume = buyBids.reduce((sum, bid) => sum + bid.quantity, 0);
        const calculatedSellVolume = sellBids.reduce((sum, bid) => sum + bid.quantity, 0);
        
        expect(round.totalBuyVolume).toBe(calculatedBuyVolume);
        expect(round.totalSellVolume).toBe(calculatedSellVolume);
      });
    });
  });

  describe('Edge Cases', () => {
    it('should handle auction with no activity', async () => {
      // Create participants that won't bid at the starting price
      const noActivityConfig = {
        ...basicConfig,
        startingPrice: 100, // Too high for buyer, too low for seller
        participants: [
          createParticipant('low-buyer', 'Low Buyer', -10, 0, -1, 20),
          createParticipant('high-seller', 'High Seller', 0, 10, 1, 150)
        ]
      };

      const simulator = new ClockAuctionSimulator(noActivityConfig);
      const result = await simulator.simulate();

      expect(result.endCondition.type).toBe('equilibrium');
      expect(result.endCondition.matchedQuantity).toBe(0);
    });

    it('should handle single participant auction', async () => {
      const singleParticipantConfig = {
        ...basicConfig,
        participants: [basicConfig.participants[0]] // Only the buyer
      };

      const simulator = new ClockAuctionSimulator(singleParticipantConfig);
      const result = await simulator.simulate();

      expect(result).toBeDefined();
      expect(result.rounds.length).toBeGreaterThan(0);
    });
  });
});
