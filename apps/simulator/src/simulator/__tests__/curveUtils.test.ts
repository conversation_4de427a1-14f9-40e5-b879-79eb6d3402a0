import { describe, it, expect } from 'vitest';
import { 
  createQuantityFunction, 
  createParticipant, 
  validateCurveParams,
  getQuantityAtPrice,
  getBidSide
} from '../utils/curveUtils';
import type { LinearCurveParams } from '../../types/types';

describe('curveUtils', () => {
  describe('createQuantityFunction', () => {
    it('should create a linear quantity function for buyers', () => {
      const buyerParams: LinearCurveParams = {
        maxBuyQuantity: -100,
        maxSellQuantity: 0,
        slope: -2,
        zeroPrice: 50
      };

      const quantityFn = createQuantityFunction(buyerParams);

      // At price 30: quantity = -2 * (30 - 50) = -2 * (-20) = 40
      // But this is positive, and buyers can't have positive quantities (maxSellQuantity = 0)
      // So it gets clamped to 0
      expect(quantityFn(30)).toBe(0);

      // At price 50 (zeroPrice): quantity should be 0
      expect(quantityFn(50)).toBe(0);

      // At price 60: quantity = -2 * (60 - 50) = -2 * 10 = -20 (wants to buy 20)
      expect(quantityFn(60)).toBe(-20);

      // At very high price: should be clamped to maxBuyQuantity
      expect(quantityFn(100)).toBe(-100); // Clamped to maxBuyQuantity
    });

    it('should create a linear quantity function for sellers', () => {
      const sellerParams: LinearCurveParams = {
        maxBuyQuantity: 0,
        maxSellQuantity: 100,
        slope: 2,
        zeroPrice: 20
      };

      const quantityFn = createQuantityFunction(sellerParams);

      // At price 30: quantity = 2 * (30 - 20) = 20
      expect(quantityFn(30)).toBe(20);

      // At price 20 (zeroPrice): quantity should be 0
      expect(quantityFn(20)).toBe(0);

      // At very high price: should be clamped to maxSellQuantity
      expect(quantityFn(100)).toBe(100); // Clamped to maxSellQuantity
    });

    it('should floor quantities to integers', () => {
      const params: LinearCurveParams = {
        maxBuyQuantity: -50,
        maxSellQuantity: 50,
        slope: 1.7, // Will create fractional quantities
        zeroPrice: 25
      };

      const quantityFn = createQuantityFunction(params);

      // At price 26: quantity = 1.7 * (26 - 25) = 1.7, should floor to 1
      expect(quantityFn(26)).toBe(1);

      // At price 27: quantity = 1.7 * (27 - 25) = 3.4, should floor to 3
      expect(quantityFn(27)).toBe(3);
    });
  });

  describe('createParticipant', () => {
    it('should create a complete participant object', () => {
      const participant = createParticipant(
        'test-participant',
        'Test Participant',
        -50,  // maxBuyQuantity
        25,   // maxSellQuantity
        1.5,  // slope
        30    // zeroPrice
      );

      expect(participant.id).toBe('test-participant');
      expect(participant.name).toBe('Test Participant');
      expect(participant.curveParams).toEqual({
        maxBuyQuantity: -50,
        maxSellQuantity: 25,
        slope: 1.5,
        zeroPrice: 30
      });
      expect(typeof participant.quantityFunction).toBe('function');

      // Test the quantity function works
      expect(participant.quantityFunction(30)).toBe(0); // At zeroPrice
      expect(participant.quantityFunction(35)).toBe(7); // 1.5 * (35 - 30) = 7.5, floored to 7
    });
  });

  describe('validateCurveParams', () => {
    it('should accept valid curve parameters', () => {
      const validParams: LinearCurveParams = {
        maxBuyQuantity: -100,
        maxSellQuantity: 50,
        slope: 2,
        zeroPrice: 25
      };

      expect(() => validateCurveParams(validParams)).not.toThrow();
    });

    it('should reject positive maxBuyQuantity', () => {
      const invalidParams: LinearCurveParams = {
        maxBuyQuantity: 50, // Should be negative
        maxSellQuantity: 50,
        slope: 2,
        zeroPrice: 25
      };

      expect(() => validateCurveParams(invalidParams))
        .toThrow('maxBuyQuantity must be negative or zero');
    });

    it('should reject negative maxSellQuantity', () => {
      const invalidParams: LinearCurveParams = {
        maxBuyQuantity: -50,
        maxSellQuantity: -25, // Should be positive
        slope: 2,
        zeroPrice: 25
      };

      expect(() => validateCurveParams(invalidParams))
        .toThrow('maxSellQuantity must be positive or zero');
    });

    it('should reject zero slope', () => {
      const invalidParams: LinearCurveParams = {
        maxBuyQuantity: -50,
        maxSellQuantity: 25,
        slope: 0, // Should not be zero
        zeroPrice: 25
      };

      expect(() => validateCurveParams(invalidParams))
        .toThrow('slope cannot be zero');
    });

    it('should reject negative zeroPrice', () => {
      const invalidParams: LinearCurveParams = {
        maxBuyQuantity: -50,
        maxSellQuantity: 25,
        slope: 2,
        zeroPrice: -10 // Should be non-negative
      };

      expect(() => validateCurveParams(invalidParams))
        .toThrow('zeroPrice must be non-negative');
    });
  });

  describe('getQuantityAtPrice', () => {
    it('should return quantity for a participant at given price', () => {
      const participant = createParticipant('test', 'Test', -100, 50, 2, 30);
      
      expect(getQuantityAtPrice(participant, 30)).toBe(0);
      expect(getQuantityAtPrice(participant, 35)).toBe(10); // 2 * (35 - 30) = 10
      expect(getQuantityAtPrice(participant, 25)).toBe(-10); // 2 * (25 - 30) = -10
    });
  });

  describe('getBidSide', () => {
    it('should return "buy" for negative quantities', () => {
      const participant = createParticipant('test', 'Test', -100, 50, 2, 30);
      
      expect(getBidSide(participant, 25)).toBe('buy'); // Negative quantity
    });

    it('should return "sell" for positive quantities', () => {
      const participant = createParticipant('test', 'Test', -100, 50, 2, 30);
      
      expect(getBidSide(participant, 35)).toBe('sell'); // Positive quantity
    });

    it('should return null for zero quantities', () => {
      const participant = createParticipant('test', 'Test', -100, 50, 2, 30);
      
      expect(getBidSide(participant, 30)).toBe(null); // Zero quantity at zeroPrice
    });
  });

  describe('Economic Behavior Validation', () => {
    it('should create rational buyer behavior (more demand at lower prices)', () => {
      // Create a buyer with positive slope but negative max quantities
      // This means: quantity = 2 * (price - 30), but clamped to [-100, 0]
      // At low prices: negative quantity (wants to buy)
      // At high prices: positive quantity (clamped to 0, no buy)
      const buyer = createParticipant('buyer', 'Buyer', -100, 0, 2, 30);

      const lowPrice = buyer.quantityFunction(20);   // 2 * (20 - 30) = -20 (wants to buy 20)
      const midPrice = buyer.quantityFunction(25);   // 2 * (25 - 30) = -10 (wants to buy 10)
      const highPrice = buyer.quantityFunction(35);  // 2 * (35 - 30) = 10 (clamped to 0)

      expect(Math.abs(lowPrice)).toBeGreaterThan(Math.abs(midPrice)); // More demand at lower prices
      expect(lowPrice).toBeLessThan(0); // Negative = buy
      expect(midPrice).toBeLessThan(0); // Negative = buy
      expect(highPrice).toBe(0); // Clamped to zero (no buy at high price)
    });

    it('should create rational seller behavior (more supply at higher prices)', () => {
      const seller = createParticipant('seller', 'Seller', 0, 100, 2, 20);
      
      const lowPrice = seller.quantityFunction(15);   // Should want to sell less/none
      const midPrice = seller.quantityFunction(30);   // Should want to sell some
      const highPrice = seller.quantityFunction(50);  // Should want to sell more
      
      expect(highPrice).toBeGreaterThan(midPrice);
      expect(midPrice).toBeGreaterThan(lowPrice);
      expect(lowPrice).toBeLessThanOrEqual(0); // Zero or negative = no sell
      expect(highPrice).toBeGreaterThan(0); // Positive = sell
    });
  });
});
