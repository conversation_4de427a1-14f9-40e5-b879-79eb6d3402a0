import {
  ClockAuctionSimulator,
  type ClockAuctionConfig,
  type ClockParticipant,
  type ClockAuctionResult,
  formatCurrency,
  formatNumber
} from '../index';
import { createParticipant } from '../utils/curveUtils';

/**
 * Double-Sided Price-Reversing Clock-Auction Example
 * 
 * This example demonstrates the clock auction implementation with curve-based participants:
 * - Linear demand/supply curves for each participant
 * - Price announcement by auctioneer
 * - Quantity-only bidding by participants based on curves
 * - Price reversal detection with different increments
 * - Allocation based on first round with maximum matched quantity
 */
async function runClockAuctionExample() {
  console.log('🕐 Starting Double-Sided Price-Reversing Clock-Auction Example\n');
  
  // Define participants with linear demand/supply curves
  const participants: ClockParticipant[] = [
    // Conservative buyer - prefers lower prices
    createParticipant(
      'conservative-buyer-1',
      'Conservative Pension Fund',
      -30,  // maxBuyQuantity
      0,    // maxSellQuantity (buyer only)
      2,    // slope
      95    // zeroPrice (high, so tends to buy at lower prices)
    ),
    
    // Aggressive buyer - willing to pay higher prices
    createParticipant(
      'aggressive-buyer-1',
      'Hedge Fund Alpha',
      -40,  // maxBuyQuantity
      5,    // maxSellQuantity (limited selling)
      1.5,  // slope
      105   // zeroPrice
    ),
    
    // Balanced trader - can buy or sell
    createParticipant(
      'balanced-trader-1',
      'AI Trading Bot',
      -35,  // maxBuyQuantity
      35,   // maxSellQuantity
      3,    // slope
      100   // zeroPrice (neutral)
    ),
    
    // Conservative seller - prefers higher prices
    createParticipant(
      'conservative-seller-1',
      'Energy Producer A',
      0,    // maxBuyQuantity (seller only)
      30,   // maxSellQuantity
      2.5,  // slope
      85    // zeroPrice
    ),
    
    // Strategic seller - moderate pricing
    createParticipant(
      'strategic-seller-1',
      'Strategic Energy Corp',
      -5,   // maxBuyQuantity (limited buying)
      35,   // maxSellQuantity
      2,    // slope
      90    // zeroPrice
    ),
    
    // Low-cost seller - willing to sell at lower prices
    createParticipant(
      'low-cost-seller-1',
      'Renewable Energy Co',
      0,    // maxBuyQuantity (seller only)
      40,   // maxSellQuantity
      3,    // slope
      80    // zeroPrice (low, so tends to sell)
    )
  ];
  
  // Configure the clock auction
  const config: ClockAuctionConfig = {
    id: 'clock-auction-demo-001',
    name: 'Energy Market Clock Auction Demo',
    startingPrice: 90,                    // Start near middle of price range
    preReversalPriceChange: 2.5,         // $2.50 increments before reversal
    postReversalPriceChange: 0.5,        // $0.50 increments after reversal
    roundDuration: 5000,                 // 5 seconds per round (fast for demo)
    participants
  };
  
  console.log('📋 Auction Configuration:');
  console.log(`  🏷️  Name: ${config.name}`);
  console.log(`  💰 Starting Price: ${formatCurrency(config.startingPrice)}`);
  console.log(`  📈 Pre-Reversal Increment: ${formatCurrency(config.preReversalPriceChange)}`);
  console.log(`  📉 Post-Reversal Increment: ${formatCurrency(config.postReversalPriceChange)}`);
  console.log(`  ⏱️  Round Duration: ${config.roundDuration}ms`);
  console.log(`  👥 Participants: ${config.participants.length}`);
  
  console.log('\n👥 Participants:');
  participants.forEach(p => {
    const { curveParams } = p;
    console.log(`  • ${p.name}:`);
    console.log(`    Max Buy: ${curveParams.maxBuyQuantity} MMLb, Max Sell: ${curveParams.maxSellQuantity} MMLb`);
    console.log(`    Slope: ${curveParams.slope} MMLb/cent, Zero Price: ${formatCurrency(curveParams.zeroPrice)}`);
  });
  
  // Create and run the simulation
  const simulator = new ClockAuctionSimulator(config);
  const result = await simulator.simulate();
  
  // Display results
  displayResults(result);
}

/**
 * Display comprehensive auction results
 */
function displayResults(result: ClockAuctionResult) {
  console.log('\n' + '='.repeat(60));
  console.log('📊 AUCTION RESULTS');
  console.log('='.repeat(60));
  
  // Basic auction info
  console.log(`\n🏁 Auction Completion:`);
  console.log(`  📅 Duration: ${formatNumber(result.duration / 1000, 1)}s`);
  console.log(`  🔄 Total Rounds: ${result.rounds.length}`);
  console.log(`  🎯 Allocation Round: ${result.allocationRound}`);
  console.log(`  🏁 End Condition: ${result.endCondition.type}`);
  console.log(`  💰 Final Price: ${formatCurrency(result.endCondition.finalPrice)}`);
  console.log(`  📦 Matched Quantity: ${formatNumber(result.endCondition.matchedQuantity)}`);
  
  // Round-by-round breakdown
  console.log(`\n📈 Round-by-Round Breakdown:`);
  console.log('  Round | Price    | Buy Vol | Sell Vol | Difference | Direction');
  console.log('  ------|----------|---------|----------|------------|----------');
  
  result.rounds.forEach(round => {
    const diff = round.totalBuyVolume - round.totalSellVolume;
    const direction = diff > 0 ? '📈 UP' : diff < 0 ? '📉 DOWN' : '⚖️  EQUAL';
    console.log(
      `  ${round.roundNumber.toString().padStart(5)} | ` +
      `${formatCurrency(round.announcedPrice).padStart(8)} | ` +
      `${formatNumber(round.totalBuyVolume).padStart(7)} | ` +
      `${formatNumber(round.totalSellVolume).padStart(8)} | ` +
      `${formatNumber(diff).padStart(10)} | ` +
      `${direction}`
    );
  });
  
  // Price history chart (simple text-based)
  console.log(`\n📊 Price Movement:`);
  displayPriceChart(result.priceHistory);
  
  // Allocation results
  console.log(`\n🎯 Final Allocations:`);
  if (result.allocations.length === 0) {
    console.log('  ❌ No allocations made');
  } else {
    const allocationSummary = summarizeAllocations(result.allocations);
    
    console.log('  Participant                | Side | Quantity | Value      | Net Position');
    console.log('  ---------------------------|------|----------|------------|-------------');
    
    Object.entries(allocationSummary).forEach(([participantId, summary]) => {
      const participant = result.rounds[0].bids.find(b => b.participantId === participantId);
      const name = participantId.substring(0, 25).padEnd(25);
      
      if (summary.buy > 0) {
        console.log(
          `  ${name} | BUY  | ${formatNumber(summary.buy).padStart(8)} | ` +
          `${formatCurrency(-summary.buyValue).padStart(10)} | ${formatNumber(summary.netQuantity)}`
        );
      }
      if (summary.sell > 0) {
        console.log(
          `  ${name} | SELL | ${formatNumber(summary.sell).padStart(8)} | ` +
          `${formatCurrency(summary.sellValue).padStart(10)} | ${formatNumber(-summary.netQuantity)}`
        );
      }
    });
  }
  
  // Constraint evolution (show final constraints)
  console.log(`\n📋 Final Constraints:`);
  const finalRound = result.rounds[result.rounds.length - 1];
  finalRound.constraints.forEach(constraint => {
    console.log(`  👤 ${constraint.participantId}:`);
    console.log(`    📈 Buy: [${constraint.minBuy}, ${constraint.maxBuy === Infinity ? '∞' : constraint.maxBuy}]`);
    console.log(`    📉 Sell: [${constraint.minSell}, ${constraint.maxSell === Infinity ? '∞' : constraint.maxSell}]`);
  });
}

/**
 * Display a simple text-based price chart
 */
function displayPriceChart(priceHistory: any[]) {
  const minPrice = Math.min(...priceHistory.map(p => p.price));
  const maxPrice = Math.max(...priceHistory.map(p => p.price));
  const priceRange = maxPrice - minPrice;
  
  if (priceRange === 0) {
    console.log('  📊 Price remained constant throughout auction');
    return;
  }
  
  const chartWidth = 50;
  
  priceHistory.forEach(point => {
    const position = Math.round(((point.price - minPrice) / priceRange) * chartWidth);
    const bar = ' '.repeat(position) + '●';
    const direction = point.direction === 'up' ? '↗' : point.direction === 'down' ? '↘' : '→';
    console.log(`  R${point.roundNumber.toString().padStart(2)} ${formatCurrency(point.price).padStart(8)} |${bar.padEnd(chartWidth + 1)}| ${direction}`);
  });
}

/**
 * Summarize allocations by participant
 */
function summarizeAllocations(allocations: any[]) {
  const summary: { [id: string]: any } = {};
  
  allocations.forEach(allocation => {
    if (!summary[allocation.participantId]) {
      summary[allocation.participantId] = {
        buy: 0, sell: 0, buyValue: 0, sellValue: 0, netQuantity: 0
      };
    }
    
    const s = summary[allocation.participantId];
    if (allocation.side === 'buy') {
      s.buy += allocation.quantity;
      s.buyValue += allocation.quantity * allocation.price;
      s.netQuantity += allocation.quantity;
    } else {
      s.sell += allocation.quantity;
      s.sellValue += allocation.quantity * allocation.price;
      s.netQuantity -= allocation.quantity;
    }
  });
  
  return summary;
}

/**
 * Run multiple auction scenarios for comparison
 */
async function runMultipleScenarios() {
  console.log('\n🔬 Running Multiple Auction Scenarios\n');
  
  const baseParticipants: ClockParticipant[] = [
    // Simple buyer - prefers lower prices
    createParticipant(
      'buyer-1',
      'Buyer A',
      -25,  // maxBuyQuantity
      0,    // maxSellQuantity (buyer only)
      2,    // slope
      100   // zeroPrice
    ),
    // Simple seller - prefers higher prices
    createParticipant(
      'seller-1',
      'Seller A',
      0,    // maxBuyQuantity (seller only)
      25,   // maxSellQuantity
      2,    // slope
      80    // zeroPrice
    )
  ];
  
  const scenarios = [
    { name: 'Low Starting Price', startingPrice: 60, preReversalPriceChange: 5, postReversalPriceChange: 1 },
    { name: 'High Starting Price', startingPrice: 120, preReversalPriceChange: 5, postReversalPriceChange: 1 },
    { name: 'Small Increments', startingPrice: 90, preReversalPriceChange: 1, postReversalPriceChange: 0.25 },
    { name: 'Large Increments', startingPrice: 90, preReversalPriceChange: 10, postReversalPriceChange: 2 }
  ];
  
  for (const scenario of scenarios) {
    console.log(`\n📊 Scenario: ${scenario.name}`);
    
    const config: ClockAuctionConfig = {
      id: `scenario-${scenario.name.toLowerCase().replace(/\s+/g, '-')}`,
      name: scenario.name,
      startingPrice: scenario.startingPrice,
      preReversalPriceChange: scenario.preReversalPriceChange,
      postReversalPriceChange: scenario.postReversalPriceChange,
      roundDuration: 1000,
      participants: baseParticipants
    };
    
    const simulator = new ClockAuctionSimulator(config);
    const result = await simulator.simulate();
    
    console.log(`  🏁 Result: ${result.endCondition.type} after ${result.rounds.length} rounds`);
    console.log(`  💰 Final Price: ${formatCurrency(result.endCondition.finalPrice)}`);
    console.log(`  📦 Matched: ${formatNumber(result.endCondition.matchedQuantity)}`);
  }
}

// Run the examples
if (import.meta.url === `file://${process.argv[1]}`) {
  runClockAuctionExample()
    .then(() => runMultipleScenarios())
    .then(() => {
      console.log('\n✅ All examples completed successfully!');
    })
    .catch(error => {
      console.error('❌ Error running examples:', error);
      process.exit(1);
    });
}

export {
  runClockAuctionExample,
  runMultipleScenarios
};