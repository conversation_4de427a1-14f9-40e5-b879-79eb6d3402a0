/**
 * Curve-Based Clock Auction Example
 * 
 * This example demonstrates the new linear demand/supply curve model where:
 * - Participants have demand/supply curves defined by 4 parameters
 * - Quantities are in MMLb (negative for buy, positive for sell)
 * - Prices are in cents/lb
 * - Max buy/sell is ±50 MMLb
 * - Auction continues until ending conditions are met (no max rounds)
 * - Price increments change after reversal
 */

import {
  ClockAuctionSimulator,
  ClockAuctionConfig,
  ClockParticipant,
  ClockAuctionResult,
  formatCurrency,
  formatNumber
} from '../src';
import { createParticipant } from '../src/utils/curveUtils';

async function runCurveBasedExample() {
  console.log('🕐 Starting Curve-Based Clock Auction Example\n');
  
  // Create participants with linear demand/supply curves
  const participants: ClockParticipant[] = [
    // Example from user: linear slope from -50 MMLb at 20 cents/lb to +50 MMLb at 40 cents/lb
    // This means: slope = (50 - (-50)) / (40 - 20) = 100 / 20 = 5 MMLb per cent
    // Zero price: at 30 cents/lb, quantity = 0
    createParticipant(
      'participant-1',
      'Linear Trader A',
      -50,  // maxBuyQuantity (negative)
      50,   // maxSellQuantity (positive)
      5,    // slope (quantity/price)
      30    // zeroPrice (price where quantity = 0)
    ),
    
    // A more aggressive buyer (willing to buy more at higher prices)
    createParticipant(
      'participant-2', 
      'Aggressive Buyer',
      -40,  // maxBuyQuantity
      20,   // maxSellQuantity (limited selling)
      3,    // slope
      35    // zeroPrice
    ),
    
    // A conservative seller (prefers to sell at higher prices)
    createParticipant(
      'participant-3',
      'Conservative Seller',
      -20,  // maxBuyQuantity (limited buying)
      45,   // maxSellQuantity
      4,    // slope
      25    // zeroPrice
    ),
    
    // A balanced trader
    createParticipant(
      'participant-4',
      'Balanced Trader',
      -35,  // maxBuyQuantity
      35,   // maxSellQuantity
      3.5,  // slope
      32    // zeroPrice
    ),
    
    // A buyer-focused participant
    createParticipant(
      'participant-5',
      'Buyer Focused',
      -50,  // maxBuyQuantity
      10,   // maxSellQuantity (very limited selling)
      2.5,  // slope
      40    // zeroPrice (high, so tends to buy)
    )
  ];
  
  // Configure the clock auction with new parameters
  const config: ClockAuctionConfig = {
    id: 'curve-based-demo-001',
    name: 'Curve-Based Double-Sided Clock Auction',
    startingPrice: 30,  // cents/lb
    preReversalPriceChange: 2,   // Large increments before reversal
    postReversalPriceChange: 0.5, // Smaller increments after reversal
    roundDuration: 30000, // 30 seconds per round
    participants
  };
  
  // Print participant curve information
  console.log('📊 Participant Curves:');
  for (const participant of participants) {
    const { curveParams } = participant;
    console.log(`  ${participant.name}:`);
    console.log(`    Max Buy: ${curveParams.maxBuyQuantity} MMLb`);
    console.log(`    Max Sell: ${curveParams.maxSellQuantity} MMLb`);
    console.log(`    Slope: ${curveParams.slope} MMLb/cent`);
    console.log(`    Zero Price: ${curveParams.zeroPrice} cents/lb`);
    
    // Show quantities at different prices
    console.log(`    Quantities:`);
    for (const price of [25, 30, 35, 40]) {
      const qty = participant.quantityFunction(price);
      const side = qty < 0 ? 'BUY' : qty > 0 ? 'SELL' : 'NONE';
      console.log(`      @ ${price} cents/lb: ${qty} MMLb (${side})`);
    }
    console.log('');
  }
  
  // Create and run simulator
  const simulator = new ClockAuctionSimulator(config);
  const result: ClockAuctionResult = await simulator.simulate();
  
  // Display results
  console.log('\n📈 Auction Results:');
  console.log(`Final Price: ${result.endCondition.finalPrice} cents/lb`);
  console.log(`Matched Quantity: ${formatNumber(result.endCondition.matchedQuantity)} MMLb`);
  console.log(`End Condition: ${result.endCondition.type}`);
  console.log(`Total Rounds: ${result.rounds.length}`);
  console.log(`Duration: ${result.duration}ms`);
  
  console.log('\n🎯 Final Allocations:');
  for (const allocation of result.allocations) {
    const participant = participants.find(p => p.id === allocation.participantId);
    console.log(`  ${participant?.name}: ${allocation.side.toUpperCase()} ${formatNumber(allocation.quantity)} MMLb @ ${allocation.price} cents/lb`);
  }
  
  console.log('\n📊 Price History:');
  for (const pricePoint of result.priceHistory) {
    console.log(`  Round ${pricePoint.roundNumber}: ${pricePoint.price} cents/lb (${pricePoint.direction}) - Buy: ${formatNumber(pricePoint.buyVolume)}, Sell: ${formatNumber(pricePoint.sellVolume)}`);
  }
  
  return result;
}

// Run the example
if (import.meta.url === `file://${process.argv[1]}`) {
  runCurveBasedExample()
    .then(() => {
      console.log('\n✅ Curve-based auction example completed successfully!');
    })
    .catch((error) => {
      console.error('❌ Error running curve-based auction example:', error);
      process.exit(1);
    });
}

export { runCurveBasedExample };