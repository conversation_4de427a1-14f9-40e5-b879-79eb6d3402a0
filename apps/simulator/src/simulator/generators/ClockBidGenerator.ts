import { v4 as uuidv4 } from 'uuid';
import {
  ClockBid,
  ClockParticipant,
  BidderConstraints
} from '@/types/types';
import { generateId } from '../utils';
import { getQuantityAtPrice, getBidSide } from '../utils/curveUtils';

/**
 * Generates quantity-only bids for clock auction participants using linear demand/supply curves
 * 
 * Each participant has a quantity function that determines their optimal bid quantity
 * based on the announced price. The auction incentivizes truthful bidding.
 */
export class ClockBidGenerator {
  private participants: ClockParticipant[];
  private roundHistory: Map<string, ClockBid[]> = new Map(); // Track bids by participant

  constructor(participants: ClockParticipant[]) {
    this.participants = participants;
    this.initializeHistory();
  }

  /**
   * Initialize bid history tracking for all participants
   */
  private initializeHistory(): void {
    for (const participant of this.participants) {
      this.roundHistory.set(participant.id, []);
    }
  }

  /**
   * Generate bids for all participants in a round using their quantity functions
   */
  async generateBids(
    roundNumber: number,
    announcedPrice: number,
    constraints: BidderConstraints[]
  ): Promise<ClockBid[]> {
    const bids: ClockBid[] = [];
    const constraintMap = new Map(constraints.map(c => [c.participantId, c]));

    for (const participant of this.participants) {
      const participantConstraints = constraintMap.get(participant.id);
      if (!participantConstraints) continue;

      const bid = this.generateParticipantBid(
        participant,
        roundNumber,
        announcedPrice,
        participantConstraints
      );

      if (bid) {
        bids.push(bid);
        // Track bid in history
        this.roundHistory.get(participant.id)?.push(bid);
      }
    }

    return bids;
  }

  /**
   * Generate a bid for a specific participant using their quantity function
   */
  private generateParticipantBid(
    participant: ClockParticipant,
    roundNumber: number,
    announcedPrice: number,
    constraints: BidderConstraints
  ): ClockBid | null {
    // Get quantity from participant's curve function
    const quantity = getQuantityAtPrice(participant, announcedPrice);
    
    // If quantity is zero, no bid
    if (quantity === 0) {
      console.log(`  ⏸️  ${participant.name}: No bid at price $${announcedPrice}`);
      return null;
    }
    
    // Determine side based on quantity sign
    const side = getBidSide(participant, announcedPrice);
    if (!side) {
      return null;
    }
    
    // Use absolute value for bid quantity (side determines buy/sell)
    const bidQuantity = Math.abs(quantity);
    
    // Apply constraints (though with rational curves, these should not be violated)
    const constrainedQuantity = this.applyConstraints(bidQuantity, side, constraints);
    
    if (constrainedQuantity <= 0) {
      console.log(`  ❌ ${participant.name}: Bid violates constraints`);
      return null;
    }

    console.log(`  💰 ${participant.name}: ${side} ${constrainedQuantity} @ $${announcedPrice}`);

    return {
      id: generateId(),
      participantId: participant.id,
      side,
      quantity: constrainedQuantity,
      timestamp: new Date(),
      roundNumber
    };
  }

  /**
   * Apply constraints to bid quantity (for validation)
   * With rational curves, constraints should not be violated
   */
  private applyConstraints(
    quantity: number,
    side: 'buy' | 'sell',
    constraints: BidderConstraints
  ): number {
    if (side === 'buy') {
      return Math.min(quantity, constraints.maxBuy);
    } else {
      return Math.min(quantity, constraints.maxSell);
    }
  }

  // All strategy-based methods removed - participants now use linear demand/supply curves

  /**
   * Get bid history for a participant
   */
  getParticipantHistory(participantId: string): ClockBid[] {
    return this.roundHistory.get(participantId) || [];
  }

  /**
   * Clear bid history (for new auction)
   */
  clearHistory(): void {
    this.initializeHistory();
  }
}