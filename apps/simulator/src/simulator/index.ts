// Clock Auction (New Implementation)
export { ClockAuctionSimulator } from './simulator/ClockAuctionSimulator';
export { ClockBidGenerator } from './generators/ClockBidGenerator';
export { ConstraintTracker } from './simulator/ConstraintTracker';
export { AllocationEngine } from './simulator/AllocationEngine';

// Curve utilities
export { createParticipant } from './utils/curveUtils';

// Legacy exports removed - use ClockAuctionSimulator and related classes instead
// All price-based bidding functionality has been replaced with quantity-only clock auctions

// Common exports
export * from '../types/types';
// Utility functions
export {
  randomBetween,
  normalRandom,
  calculateVolatility,
  formatCurrency,
  formatNumber,
  calculatePercentageChange,
  clamp,
  generateId,
  deepClone,
  debounce,
  throttle
} from './utils';