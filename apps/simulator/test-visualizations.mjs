import puppeteer from 'puppeteer';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const config = {
  mainAppUrl: 'http://localhost:5173',
  storybookUrl: 'http://localhost:6006',
  screenshotDir: './screenshots',
  timeout: 30000,
  viewport: { width: 1200, height: 800 }
};

// Ensure screenshots directory exists
if (!fs.existsSync(config.screenshotDir)) {
  fs.mkdirSync(config.screenshotDir, { recursive: true });
}

async function testVisualizationApp() {
  console.log('🚀 Starting AU25 Visualization Testing...');
  
  const browser = await puppeteer.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    await page.setViewport(config.viewport);
    
    // Collect console logs and errors
    const logs = [];
    const errors = [];
    
    page.on('console', msg => {
      const type = msg.type();
      const text = msg.text();
      logs.push({ type, text, timestamp: new Date().toISOString() });
      
      if (type === 'error' || type === 'warning') {
        console.log(`📝 Console ${type}: ${text}`);
        errors.push({ type, text, timestamp: new Date().toISOString() });
      }
    });
    
    page.on('pageerror', error => {
      console.error('❌ Page Error:', error.message);
      errors.push({ type: 'pageerror', text: error.message, timestamp: new Date().toISOString() });
    });
    
    // Test Main Application
    console.log('\n📊 Testing Main Visualization App...');
    await testMainApp(page);
    
    // Test Storybook
    console.log('\n📚 Testing Storybook Stories...');
    await testStorybook(page);
    
    // Generate test report
    await generateTestReport(logs, errors);
    
    console.log('\n✅ All tests completed successfully!');
    console.log(`📸 Screenshots saved to: ${path.resolve(config.screenshotDir)}`);
    
  } catch (error) {
    console.error('❌ Test execution failed:', error);
    process.exit(1);
  } finally {
    await browser.close();
  }
}

async function testMainApp(page) {
  try {
    console.log(`   Navigating to ${config.mainAppUrl}...`);
    await page.goto(config.mainAppUrl, { waitUntil: 'networkidle2', timeout: config.timeout });
    
    // Wait for charts to load
    await page.waitForSelector('.js-plotly-plot', { timeout: 10000 });
    console.log('   ✓ Charts loaded successfully');
    
    // Take full page screenshot
    await page.screenshot({
      path: path.join(config.screenshotDir, 'main-app-full.png'),
      fullPage: true
    });
    console.log('   ✓ Full page screenshot captured');
    
    // Test Price Timeline Chart
    const priceChart = await page.$('.js-plotly-plot');
    if (priceChart) {
      await priceChart.screenshot({
        path: path.join(config.screenshotDir, 'price-timeline-chart.png')
      });
      console.log('   ✓ Price Timeline Chart screenshot captured');
    }
    
    // Test Supply Demand Chart (should be the second chart)
    const charts = await page.$$('.js-plotly-plot');
    if (charts.length > 1) {
      await charts[1].screenshot({
        path: path.join(config.screenshotDir, 'supply-demand-chart.png')
      });
      console.log('   ✓ Supply Demand Chart screenshot captured');
    }
    
    // Test chart interactivity
    if (charts.length > 0) {
      await charts[0].hover();
      await page.waitForTimeout(1000);
      console.log('   ✓ Chart hover interaction tested');
    }
    
    // Validate page content
    const title = await page.$eval('h1', el => el.textContent);
    if (title.includes('AU25 Auction Visualization')) {
      console.log('   ✓ Page title validation passed');
    } else {
      console.warn('   ⚠️  Page title validation failed');
    }
    
  } catch (error) {
    console.error('   ❌ Main app test failed:', error.message);
    throw error;
  }
}

async function testStorybook(page) {
  try {
    console.log(`   Navigating to ${config.storybookUrl}...`);
    await page.goto(config.storybookUrl, { waitUntil: 'networkidle2', timeout: config.timeout });
    
    // Wait for Storybook to load
    await page.waitForSelector('[data-testid="sidebar-wrapper"]', { timeout: 10000 });
    console.log('   ✓ Storybook loaded successfully');
    
    // Test SupplyDemandChart stories
    const stories = [
      'Default',
      'Elastic Supply',
      'Inelastic Demand',
      'Volatile Market',
      'No Equilibrium',
      'Early Round'
    ];
    
    for (const story of stories) {
      try {
        console.log(`   Testing story: ${story}...`);
        
        // Navigate to the story
        const storyUrl = `${config.storybookUrl}/?path=/story/charts-supplydemandchart--${story.toLowerCase().replace(/\s+/g, '-')}`;
        await page.goto(storyUrl, { waitUntil: 'networkidle2', timeout: config.timeout });
        
        // Wait for the chart to render
        await page.waitForSelector('.js-plotly-plot', { timeout: 10000 });
        
        // Take screenshot
        const screenshotName = `storybook-supply-demand-${story.toLowerCase().replace(/\s+/g, '-')}.png`;
        await page.screenshot({
          path: path.join(config.screenshotDir, screenshotName)
        });
        
        console.log(`   ✓ Story "${story}" screenshot captured`);
        
      } catch (storyError) {
        console.warn(`   ⚠️  Story "${story}" test failed:`, storyError.message);
      }
    }
    
    // Test PriceTimelineChart stories
    console.log('   Testing PriceTimelineChart stories...');
    const priceTimelineUrl = `${config.storybookUrl}/?path=/story/charts-pricetimelinechart--default`;
    await page.goto(priceTimelineUrl, { waitUntil: 'networkidle2', timeout: config.timeout });
    
    await page.waitForSelector('.js-plotly-plot', { timeout: 10000 });
    await page.screenshot({
      path: path.join(config.screenshotDir, 'storybook-price-timeline-default.png')
    });
    console.log('   ✓ PriceTimelineChart default story screenshot captured');
    
  } catch (error) {
    console.error('   ❌ Storybook test failed:', error.message);
    throw error;
  }
}

async function generateTestReport(logs, errors) {
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      totalLogs: logs.length,
      totalErrors: errors.length,
      errorTypes: errors.reduce((acc, error) => {
        acc[error.type] = (acc[error.type] || 0) + 1;
        return acc;
      }, {})
    },
    logs: logs,
    errors: errors
  };
  
  const reportPath = path.join(config.screenshotDir, 'test-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  console.log('\n📋 Test Report Summary:');
  console.log(`   Total console logs: ${report.summary.totalLogs}`);
  console.log(`   Total errors/warnings: ${report.summary.totalErrors}`);
  
  if (report.summary.totalErrors > 0) {
    console.log('   Error breakdown:');
    Object.entries(report.summary.errorTypes).forEach(([type, count]) => {
      console.log(`     ${type}: ${count}`);
    });
  }
  
  console.log(`   Full report saved to: ${reportPath}`);
}

// Run the tests
if (import.meta.url === `file://${process.argv[1]}`) {
  testVisualizationApp().catch(error => {
    console.error('❌ Test suite failed:', error);
    process.exit(1);
  });
}

export { testVisualizationApp };