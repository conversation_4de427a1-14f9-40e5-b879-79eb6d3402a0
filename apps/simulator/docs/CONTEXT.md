# AU25 Auction Simulator - Complete Context Documentation

## Project Overview

The AU25 Auction Simulator is a TypeScript-based simulation engine for clock auctions, implementing a curve-based participant model. This simulator is part of a larger auction analysis ecosystem that includes visualization tools and dashboard applications.

## Project Structure

### Simulator App Structure
```
apps/simulator/
├── src/
│   ├── index.ts           # Main export file
│   ├── types.ts           # Core type definitions
│   ├── utils.ts           # Utility functions
│   ├── generators/        # Data generation utilities
│   │   ├── index.ts
│   │   ├── participants.ts
│   │   └── constraints.ts
│   └── simulator/         # Core simulation logic
│       ├── index.ts
│       └── clock-auction.ts
├── examples/              # Usage examples
│   └── clock-auction-example.ts
├── docs/                  # Documentation
│   ├── CONTEXT.md         # This file
│   └── plan.md           # Implementation plan
├── package.json
├── tsconfig.json
└── README.md
```

## Core Architecture

### Participant Model
The simulator uses a **curve-based participant model** where each participant's bidding behavior is defined by mathematical curves rather than discrete bid amounts.

#### Key Components:

1. **ClockParticipant Interface**
   ```typescript
   interface ClockParticipant {
     id: string;
     name: string;
     quantityFunction: QuantityFunction;
     constraints: BidderConstraints;
   }
   ```

2. **QuantityFunction (Curve Definition)**
   ```typescript
   interface QuantityFunction {
     type: 'linear' | 'exponential' | 'logarithmic' | 'step';
     params: LinearCurveParams | ExponentialCurveParams | LogarithmicCurveParams | StepCurveParams;
   }
   ```

3. **LinearCurveParams (Most Common)**
   ```typescript
   interface LinearCurveParams {
     maxQuantity: number;    // Maximum quantity participant will bid for
     reservePrice: number;   // Minimum price participant will accept
     slope: number;          // Rate of quantity decrease as price increases
   }
   ```

### Auction Mechanics

#### Clock Auction Process:
1. **Initialization**: Set starting price and participant curves
2. **Round Execution**: 
   - Calculate quantity demanded at current price for each participant
   - Check if total demand ≤ total supply
   - If excess demand exists, increase price and continue
3. **Termination**: When demand ≤ supply or other end conditions met
4. **Allocation**: Distribute quantities based on final round results

#### End Conditions:
```typescript
interface AuctionEndCondition {
  type: 'demand_supply_balance' | 'max_rounds' | 'price_ceiling' | 'no_active_bidders';
  value?: number;
  round: number;
  timestamp: Date;
}
```

### Data Structures

#### Core Result Interface:
```typescript
interface ClockAuctionResult {
  auctionId: string;
  rounds: ClockAuctionRound[];
  endCondition: AuctionEndCondition;
  allocationRound: number;
  allocations: Allocation[];
  priceHistory: PricePoint[];
  duration: number; // milliseconds
}
```

#### Round Data:
```typescript
interface ClockAuctionRound {
  round: number;
  price: number;
  bids: ClockBid[];
  totalDemand: number;
  totalSupply: number;
  excessDemand: number;
  timestamp: Date;
}
```

#### Bid Structure:
```typescript
interface ClockBid {
  participantId: string;
  quantity: number;
  price: number;
  round: number;
  timestamp: Date;
}
```

## Key Features

### 1. Curve-Based Modeling
- **Linear Curves**: Most common, representing steady demand decrease with price increase
- **Exponential Curves**: Rapid demand drop-off at certain price points
- **Logarithmic Curves**: Gradual demand decrease, more resistant to price increases
- **Step Curves**: Discrete quantity levels at specific price thresholds

### 2. Constraint System
```typescript
interface BidderConstraints {
  maxTotalQuantity?: number;     // Overall quantity limit
  maxTotalValue?: number;        // Budget constraint
  minQuantityPerBid?: number;    // Minimum bid size
  maxQuantityPerBid?: number;    // Maximum bid size
}
```

### 3. Price Discovery
- Automatic price increment calculation
- Configurable price steps and ceilings
- Support for multiple price discovery algorithms

### 4. Real-time Simulation
- Round-by-round execution
- Timestamp tracking for all events
- Detailed logging and state tracking

## Configuration

### Auction Configuration:
```typescript
interface ClockAuctionConfig {
  startingPrice: number;
  priceIncrement: number;
  maxRounds: number;
  totalSupply: number;
  participants: ClockParticipant[];
  endConditions: AuctionEndCondition[];
}
```

### Example Configuration:
```typescript
const config: ClockAuctionConfig = {
  startingPrice: 10,
  priceIncrement: 5,
  maxRounds: 20,
  totalSupply: 1000,
  participants: [
    {
      id: 'participant-1',
      name: 'Aggressive Bidder',
      quantityFunction: {
        type: 'linear',
        params: {
          maxQuantity: 500,
          reservePrice: 15,
          slope: -2.5
        }
      },
      constraints: {
        maxTotalQuantity: 500,
        maxTotalValue: 15000
      }
    }
    // ... more participants
  ],
  endConditions: [
    { type: 'demand_supply_balance' },
    { type: 'max_rounds', value: 20 }
  ]
};
```

## Usage Examples

### Basic Simulation:
```typescript
import { ClockAuction } from '@au25/simulator';

const auction = new ClockAuction(config);
const result = await auction.run();

console.log(`Auction completed in ${result.rounds.length} rounds`);
console.log(`Final price: ${result.priceHistory[result.priceHistory.length - 1].price}`);
console.log(`Allocations:`, result.allocations);
```

### Step-by-Step Execution:
```typescript
const auction = new ClockAuction(config);

while (!auction.isComplete()) {
  const roundResult = await auction.executeRound();
  console.log(`Round ${roundResult.round}: Price ${roundResult.price}, Demand ${roundResult.totalDemand}`);
}

const finalResult = auction.getResult();
```

## Data Generation

The simulator includes utilities for generating test data:

### Participant Generation:
```typescript
import { generateParticipants } from '@au25/simulator/generators';

const participants = generateParticipants({
  count: 10,
  curveTypes: ['linear', 'exponential'],
  priceRange: [10, 100],
  quantityRange: [50, 500]
});
```

### Constraint Generation:
```typescript
import { generateConstraints } from '@au25/simulator/generators';

const constraints = generateConstraints({
  budgetRange: [5000, 50000],
  quantityLimits: [100, 1000]
});
```

## Integration Points

### With Visualization App
The simulator exports `ClockAuctionResult` data that can be consumed by the visualization app:

```typescript
// In visualization app
import type { ClockAuctionResult } from '@au25/simulator';

function visualizeAuction(result: ClockAuctionResult) {
  // Create charts from result.priceHistory, result.rounds, etc.
}
```

### Data Export Formats
- **JSON**: Complete result serialization
- **CSV**: Round-by-round data for spreadsheet analysis
- **Real-time**: WebSocket streaming for live visualization

## Development Workflow

### Running Examples:
```bash
cd apps/simulator
pnpm install
pnpm run example  # Runs clock-auction-example.ts
```

### Testing:
```bash
pnpm test         # Run unit tests
pnpm test:watch   # Watch mode
pnpm test:coverage # Coverage report
```

### Building:
```bash
pnpm build        # TypeScript compilation
pnpm build:watch  # Watch mode
```

## Recent Changes

### Migration to Curve-Based Model
- **Previous**: Discrete bid amounts per participant
- **Current**: Mathematical curves defining quantity as function of price
- **Benefits**: More realistic modeling, smoother price discovery, better analysis capabilities

### Key Files Updated:
- `src/types.ts`: Added curve interfaces and updated participant model
- `src/simulator/clock-auction.ts`: Implemented curve-based quantity calculation
- `examples/clock-auction-example.ts`: Updated to demonstrate new model
- `README.md`: Comprehensive documentation of new architecture

## Performance Considerations

### Optimization Strategies:
- **Curve Caching**: Pre-calculate quantity values for common price points
- **Batch Processing**: Process multiple participants simultaneously
- **Memory Management**: Efficient storage of round history

### Benchmarks:
- **Small Auction** (5 participants, 10 rounds): ~10ms
- **Medium Auction** (50 participants, 20 rounds): ~100ms
- **Large Auction** (500 participants, 50 rounds): ~1s

## Troubleshooting

### Common Issues:

1. **Infinite Rounds**: Check that price increments and participant curves allow for convergence
2. **No Allocations**: Verify that at least one participant has a reserve price below the final price
3. **Constraint Violations**: Ensure participant constraints are compatible with their curves

### Debug Mode:
```typescript
const auction = new ClockAuction(config, { debug: true });
// Enables detailed logging of each round
```

## Future Enhancements

### Planned Features:
- **Multi-Product Auctions**: Support for multiple products in single auction
- **Dynamic Constraints**: Constraints that change based on auction progress
- **Advanced Curves**: Polynomial and custom curve types
- **Participant Learning**: Adaptive bidding strategies

### Integration Roadmap:
- **Real-time Visualization**: Live chart updates during simulation
- **API Endpoints**: REST API for remote simulation execution
- **Database Integration**: Persistent storage of auction results
- **Machine Learning**: Participant behavior prediction

## Dependencies

### Runtime Dependencies:
- **TypeScript**: Type safety and modern JavaScript features
- **Date-fns**: Date manipulation utilities

### Development Dependencies:
- **Jest**: Testing framework
- **ESLint**: Code linting
- **Prettier**: Code formatting

## Version History

- **v0.3.0**: Curve-based participant model implementation
- **v0.2.0**: Enhanced constraint system
- **v0.1.0**: Initial clock auction implementation

## Contact & Support

For questions about the simulator:
1. Check this CONTEXT.md file
2. Review the implementation plan in `docs/plan.md`
3. Examine examples in `examples/` directory
4. Check the main README.md for API documentation

## Quick Start for New Chat Sessions

When starting a new chat about this simulator:

1. **Current State**: The simulator is fully functional with curve-based participant modeling
2. **Key Files**: Focus on `src/types.ts`, `src/simulator/clock-auction.ts`, and examples
3. **Recent Work**: Migration from discrete bids to mathematical curves completed
4. **Next Steps**: Refer to `docs/plan.md` for visualization implementation roadmap
5. **Architecture**: Monorepo with separate apps for simulation, visualization, and dashboard

This context should provide complete understanding for continuing development, debugging, or extending the auction simulator functionality.