# Auction Visualization Implementation Plan

## Overview
This plan outlines the implementation of comprehensive auction visualization components for the AU25 Auction Simulator. The system will provide real-time and post-auction analysis through interactive charts and dashboards.

## Project Structure

### Apps Architecture
- `apps/simulator/` - Core auction simulation engine (moved from packages/)
- `apps/visualization/` - New React app with Storybook for auction visualizations
- Complete separation between simulator and dashboard applications

## Phase 1: Foundation Setup

### 1.1 Project Restructuring
- [x] Move `packages/simulator/` to `apps/simulator/`
- [x] Create new `apps/visualization/` React application
- [x] Setup Storybook for component development
- [x] Configure build tools and dependencies

### 1.2 Data Layer Enhancement
- Enhance `ClockAuctionResult` interface for visualization needs
- Add data transformation utilities
- Create mock data generators for development

## Phase 2: Core Visualization Components

### 2.1 Price Discovery Timeline Chart
**Purpose**: Track price evolution across auction rounds
**Data Source**: `ClockAuctionResult.priceHistory[]`
**Implementation**: Plotly.js line chart with interactive zoom
**Features**:
- Multi-series for different products
- Round markers and annotations
- Price target indicators
- Responsive design

### 2.2 Supply & Demand Curve Visualization
**Purpose**: Show participant curves and market equilibrium
**Data Source**: `ClockParticipant.quantityFunction` + `ClockAuctionResult.rounds[]`
**Implementation**: Plotly.js scatter/line chart
**Features**:
- Individual participant curves
- Aggregate supply/demand
- Equilibrium point highlighting
- Interactive curve selection

### 2.3 Round-by-Round Volume Analysis
**Purpose**: Analyze bidding activity and volume changes
**Data Source**: `ClockAuctionResult.rounds[].bids[]`
**Implementation**: Recharts bar/area chart
**Features**:
- Volume by round and participant
- Cumulative volume tracking
- Activity heatmap overlay
- Export functionality

## Phase 3: Advanced Analytics

### 3.1 Participant Allocation Sankey Diagram
**Purpose**: Visualize final allocations and participant flows
**Data Source**: `ClockAuctionResult.allocations[]`
**Implementation**: Plotly.js Sankey diagram
**Features**:
- Participant → Product → Quantity flows
- Color-coded by allocation efficiency
- Interactive node exploration
- Allocation summary tooltips

### 3.2 Constraint Tracking Heatmap
**Purpose**: Monitor constraint violations and participant limits
**Data Source**: `ClockParticipant.constraints` + round data
**Implementation**: Plotly.js heatmap
**Features**:
- Constraint status by participant/round
- Violation severity indicators
- Constraint type filtering
- Real-time updates during simulation

## Phase 4: Integration & Dashboard

### 4.1 Visualization Dashboard
- Create main dashboard component
- Implement layout management
- Add chart configuration panels
- Setup data refresh mechanisms

### 4.2 Storybook Documentation
- Component stories for all visualizations
- Interactive controls and knobs
- Data variation examples
- Performance benchmarks

## Technical Implementation

### Tech Stack
- **Frontend**: React 18 + TypeScript
- **Charts**: Plotly.js (primary), Recharts (secondary)
- **Styling**: Tailwind CSS
- **Development**: Storybook, Vite
- **Testing**: Vitest, React Testing Library

### Dependencies
```json
{
  "react-plotly.js": "^2.6.0",
  "plotly.js": "^2.26.0",
  "recharts": "^2.8.0",
  "@types/plotly.js": "^2.12.29",
  "tailwindcss": "^3.3.0",
  "@storybook/react-vite": "^7.5.0"
}
```

### Color Scheme Strategy
```typescript
const AUCTION_COLORS = {
  primary: '#2563eb',    // Blue for main data
  secondary: '#dc2626',  // Red for constraints/limits
  success: '#16a34a',    // Green for successful allocations
  warning: '#ca8a04',    // Yellow for warnings
  neutral: '#6b7280',    // Gray for inactive/background
  participants: ['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6']
};
```

### Data Transform Patterns
```typescript
// Price history transformation
const transformPriceHistory = (result: ClockAuctionResult) => {
  return result.priceHistory.map(point => ({
    round: point.round,
    price: point.price,
    timestamp: point.timestamp,
    product: point.productId
  }));
};

// Participant curve data
const transformParticipantCurves = (participants: ClockParticipant[]) => {
  return participants.map(p => ({
    id: p.id,
    name: p.name,
    curve: p.quantityFunction,
    constraints: p.constraints
  }));
};
```

## Component Structure

```
apps/visualization/src/
├── components/
│   ├── charts/
│   │   ├── PriceTimelineChart.tsx
│   │   ├── SupplyDemandChart.tsx
│   │   ├── VolumeAnalysisChart.tsx
│   │   ├── AllocationSankeyChart.tsx
│   │   └── ConstraintHeatmap.tsx
│   ├── dashboard/
│   │   ├── AuctionDashboard.tsx
│   │   ├── ChartContainer.tsx
│   │   └── ControlPanel.tsx
│   └── common/
│       ├── LoadingSpinner.tsx
│       ├── ErrorBoundary.tsx
│       └── ExportButton.tsx
├── hooks/
│   ├── useAuctionData.ts
│   ├── useChartResize.ts
│   └── useDataTransform.ts
├── utils/
│   ├── dataTransforms.ts
│   ├── chartHelpers.ts
│   └── exportUtils.ts
├── types/
│   └── visualization.ts
└── stories/
    ├── charts/
    └── dashboard/
```

## Success Metrics
- All 5 core visualization components implemented
- Storybook stories with interactive controls
- Responsive design across devices
- Export functionality for all charts
- Performance: <100ms render time for typical datasets
- Type safety: 100% TypeScript coverage

## Development Workflow
1. Component development in Storybook
2. Data integration with simulator
3. Dashboard composition
4. Testing and optimization
5. Documentation and examples

## Quick Start Commands
```bash
# Setup
pnpm install

# Development
pnpm dev:visualization    # Start React app
pnpm storybook           # Start Storybook
pnpm dev:simulator       # Start simulator

# Testing
pnpm test:visualization
pnpm test:simulator

# Build
pnpm build:visualization
pnpm build:simulator
```

## Integration Points
- **Simulator**: Data export via `ClockAuctionResult`
- **API**: RESTful endpoints for real-time data
- **Storybook**: Component development and documentation
- **Dashboard**: Optional integration for unified view

## Notes
- All visualizations are standalone components
- No dependencies on existing dashboard app
- Focus on performance and interactivity
- Comprehensive documentation for future development