<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive 3D Auction Chart</title>
    <style>
        body { margin: 0; overflow: hidden; background-color: #111; color: #e0e0e0; font-family: Arial, sans-serif; }
        canvas { display: block; }
        .controls-panel {
            position: absolute;
            right: 20px;
            background-color: rgba(30, 30, 30, 0.9);
            padding: 10px 15px;
            border-radius: 5px;
            border: 1px solid #444;
            width: 230px; /* Adjusted width for better layout */
            font-size: 12px;
        }
        #legend { top: 20px; }
        #slidersContainer { /* Will be positioned by JS below legend */ }

        .controls-panel h4 { margin-top: 0; margin-bottom: 10px; font-size: 14px; border-bottom: 1px solid #444; padding-bottom: 5px; }
        .controls-panel div { margin-bottom: 8px; display: flex; align-items: center; justify-content: space-between;}
        .controls-panel label { flex-basis: 95px; /* Give label fixed space */ }
        .controls-panel input[type="range"] { flex-grow: 1; margin: 0 5px; }
        .controls-panel span.value-display { flex-basis: 35px; text-align: right; }
    </style>
</head>
<body>
    <div id="container"></div>
    <div id="legend" class="controls-panel"><h4>Legend</h4></div>
    <div id="slidersContainer" class="controls-panel">
        <h4>Axis Scales</h4>
        <div><label for="xScale">X-Scale:</label><input type="range" id="xScale" min="0.2" max="2.5" step="0.1" value="1.0"><span id="xScaleValue" class="value-display">1.0</span></div>
        <div><label for="yScale">Y-Scale:</label><input type="range" id="yScale" min="0.1" max="2.0" step="0.05" value="0.5"><span id="yScaleValue" class="value-display">0.5</span></div>
        <div><label for="zScale">Z-Scale:</label><input type="range" id="zScale" min="0.2" max="2.5" step="0.1" value="1.0"><span id="zScaleValue" class="value-display">1.0</span></div>
        <h4 style="margin-top:15px;">Orbit Limits (rad)</h4>
        <div><label for="minAzimuth">Min Azimuth:</label><input type="range" id="minAzimuth" min="-3.14" max="3.14" step="0.01" value="-3.14"><span id="minAzimuthValue" class="value-display">-π</span></div>
        <div><label for="maxAzimuth">Max Azimuth:</label><input type="range" id="maxAzimuth" min="-3.14" max="3.14" step="0.01" value="3.14"><span id="maxAzimuthValue" class="value-display">π</span></div>
        <div><label for="minPolar">Min Polar:</label><input type="range" id="minPolar" min="0.01" max="3.14" step="0.01" value="0.1"><span id="minPolarValue" class="value-display">0.1</span></div>
        <div><label for="maxPolar">Max Polar:</label><input type="range" id="maxPolar" min="0.01" max="3.14" step="0.01" value="3.0"><span id="maxPolarValue" class="value-display">3.0</span></div>
    </div>

    <script type="importmap">
        {
            "imports": {
                "three": "https://unpkg.com/three@0.158.0/build/three.module.js",
                "three/addons/": "https://unpkg.com/three@0.158.0/examples/jsm/"
            }
        }
    </script>

    <script type="module">
        import * as THREE from 'three';
        import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
        import { FontLoader } from 'three/addons/loaders/FontLoader.js';
        import { TextGeometry } from 'three/addons/geometries/TextGeometry.js';

        let scene, camera, renderer, controls;
        const traderObjectsGroup = new THREE.Group();
        const axesGroup = new THREE.Group();

        const NUM_TRADERS = 5;
        const NUM_ROUNDS = 10;
        const INITIAL_QUANTITY = 100;
        const Z_SPACING_PER_TRADER_UNSCALED = 8;
        const MARKER_SIZE = 0.4;

        const X_MAX_DATA = NUM_ROUNDS;
        const Y_MAX_DATA = INITIAL_QUANTITY;
        const Z_MAX_DATA_UNSCALED = (NUM_TRADERS > 0 ? (NUM_TRADERS - 1) : 0) * Z_SPACING_PER_TRADER_UNSCALED;

        // Initialize scale factors in JavaScript with defaults
        // These defaults should match the 'value' attributes in the HTML sliders
        let xScale = 1.0;
        let yScale = 0.5;
        let zScale = 1.0;

        const TRADER_COLORS = [0xffde3d, 0xffb732, 0xff9234, 0xff6d3a, 0xff4530];
        let helvetikerFont = null;

        function generateAuctionData(numTraders, numRounds, initialQuantity) { /* ... (same as before) ... */
            const tradersData = [];
            for (let t = 0; t < numTraders; t++) {
                const trader = { traderId: `Trader ${t + 1}`, bids: [] };
                let currentQuantity = initialQuantity;
                for (let r = 1; r <= numRounds; r++) {
                    if (r === 1) trader.bids.push({ round: r, quantity: initialQuantity });
                    else {
                        if (currentQuantity > 0) {
                            const decrease = Math.floor(Math.random() * 5) + 1;
                            currentQuantity = Math.max(0, currentQuantity - decrease);
                        }
                        trader.bids.push({ round: r, quantity: currentQuantity });
                    }
                }
                tradersData.push(trader);
            }
            return tradersData;
        }
        const auctionData = generateAuctionData(NUM_TRADERS, NUM_ROUNDS, INITIAL_QUANTITY);

        function createText(text, unscaledPos, color, size, rotation = new THREE.Euler(0,0,0), parent = axesGroup) {
            if (!helvetikerFont) { console.error("Font not loaded for text:", text); return; }
            const textGeo = new TextGeometry(text, { font: helvetikerFont, size: size, height: size * 0.05, curveSegments: 3 });
            textGeo.center();
            const textMat = new THREE.MeshBasicMaterial({ color: color });
            const textMesh = new THREE.Mesh(textGeo, textMat);
            textMesh.position.set(unscaledPos.x * xScale, unscaledPos.y * yScale, unscaledPos.z * zScale);
            textMesh.rotation.copy(rotation);
            parent.add(textMesh);
        }

        function createAxesAndGrid() {
            axesGroup.clear();
            const axisColor = 0x999999; const gridColor = 0x444444; const tickColor = 0xcccccc;
            const labelColor = 0xffffff; const axisLabelSize = 2.0; const tickLabelSize = 1.5;
            const axisLineMaterial = new THREE.LineBasicMaterial({ color: axisColor });
            const gridLineMaterial = new THREE.LineBasicMaterial({ color: gridColor });

            const xExtent = (X_MAX_DATA + 1) * xScale; const yExtent = (Y_MAX_DATA + 5) * yScale;
            const zExtent = (Z_MAX_DATA_UNSCALED + Z_SPACING_PER_TRADER_UNSCALED * 0.5) * zScale;

            axesGroup.add(new THREE.Line(new THREE.BufferGeometry().setFromPoints([new THREE.Vector3(0,0,0), new THREE.Vector3(xExtent, 0, 0)]), axisLineMaterial));
            axesGroup.add(new THREE.Line(new THREE.BufferGeometry().setFromPoints([new THREE.Vector3(0,0,0), new THREE.Vector3(0, yExtent, 0)]), axisLineMaterial));
            axesGroup.add(new THREE.Line(new THREE.BufferGeometry().setFromPoints([new THREE.Vector3(0,0,0), new THREE.Vector3(0, 0, zExtent)]), axisLineMaterial));

            createText('Round', new THREE.Vector3(X_MAX_DATA / 2, -axisLabelSize*2.5 / yScale, 0), labelColor, axisLabelSize);
            createText('Quantity', new THREE.Vector3(-axisLabelSize*3 / xScale, Y_MAX_DATA / 2, 0), labelColor, axisLabelSize, new THREE.Euler(0,0,Math.PI/2));
            if (NUM_TRADERS > 1) createText('Trader', new THREE.Vector3(0, -axisLabelSize*2.5 / yScale, Z_MAX_DATA_UNSCALED / 2), labelColor, axisLabelSize, new THREE.Euler(0,Math.PI/2,0));

            for (let i = 0; i <= Y_MAX_DATA; i += 20) {
                if (i === 0 && Y_MAX_DATA > 0) continue;
                createText(i.toString(), new THREE.Vector3(-tickLabelSize*0.7 / xScale, i, 0), tickColor, tickLabelSize);
                const tickLineGeo = new THREE.BufferGeometry().setFromPoints([new THREE.Vector3(-0.5 * xScale,i*yScale,0), new THREE.Vector3(0.5*xScale,i*yScale,0)]);
                axesGroup.add(new THREE.LineSegments(tickLineGeo, new THREE.LineBasicMaterial({color: axisColor})));
            }
            for (let i = 0; i <= X_MAX_DATA; i += 2) {
                if (i === 0 && X_MAX_DATA > 0) continue;
                createText(i.toString(), new THREE.Vector3(i, -tickLabelSize*1.2 / yScale, 0), tickColor, tickLabelSize*0.8);
            }

            const yGridStep = 10;
            for (let i = 0; i <= X_MAX_DATA; i++) {
                const points = [new THREE.Vector3(i*xScale, 0, 0), new THREE.Vector3(i*xScale, 0, Z_MAX_DATA_UNSCALED*zScale)];
                axesGroup.add(new THREE.LineSegments(new THREE.BufferGeometry().setFromPoints(points), gridLineMaterial));
            }
            for (let i = 0; i <= NUM_TRADERS -1 ; i++) {
                const zPos = i * Z_SPACING_PER_TRADER_UNSCALED;
                const points = [new THREE.Vector3(0, 0, zPos*zScale), new THREE.Vector3(X_MAX_DATA*xScale, 0, zPos*zScale)];
                axesGroup.add(new THREE.LineSegments(new THREE.BufferGeometry().setFromPoints(points), gridLineMaterial));
            }
             if (NUM_TRADERS === 0 && Z_MAX_DATA_UNSCALED === 0) {
                const points = [new THREE.Vector3(0, 0, 0), new THREE.Vector3(X_MAX_DATA*xScale, 0, 0)];
                axesGroup.add(new THREE.LineSegments(new THREE.BufferGeometry().setFromPoints(points), gridLineMaterial));
            }
            for (let i = 0; i <= X_MAX_DATA; i++) {
                const points = [new THREE.Vector3(i*xScale, 0, 0), new THREE.Vector3(i*xScale, Y_MAX_DATA*yScale, 0)];
                axesGroup.add(new THREE.LineSegments(new THREE.BufferGeometry().setFromPoints(points), gridLineMaterial));
            }
            for (let i = 0; i <= Y_MAX_DATA; i += yGridStep) {
                const points = [new THREE.Vector3(0, i*yScale, 0), new THREE.Vector3(X_MAX_DATA*xScale, i*yScale, 0)];
                axesGroup.add(new THREE.LineSegments(new THREE.BufferGometry().setFromPoints(points), gridLineMaterial));
            }
            for (let i = 0; i <= NUM_TRADERS -1; i++) {
                const zPos = i * Z_SPACING_PER_TRADER_UNSCALED;
                const points = [new THREE.Vector3(0, 0, zPos*zScale), new THREE.Vector3(0, Y_MAX_DATA*yScale, zPos*zScale)];
                axesGroup.add(new THREE.LineSegments(new THREE.BufferGeometry().setFromPoints(points), gridLineMaterial));
            }
            if (NUM_TRADERS === 0 && Z_MAX_DATA_UNSCALED === 0) {
                const points = [new THREE.Vector3(0, 0, 0), new THREE.Vector3(0, Y_MAX_DATA*yScale, 0)];
                axesGroup.add(new THREE.LineSegments(new THREE.BufferGeometry().setFromPoints(points), gridLineMaterial));
            }
            for (let i = 0; i <= Y_MAX_DATA; i += yGridStep) {
                const points = [new THREE.Vector3(0, i*yScale, 0), new THREE.Vector3(0, i*yScale, Z_MAX_DATA_UNSCALED*zScale)];
                axesGroup.add(new THREE.LineSegments(new THREE.BufferGeometry().setFromPoints(points), gridLineMaterial));
            }
            scene.add(axesGroup);
        }

        function plotData() {
            traderObjectsGroup.clear();
            const legendDiv = document.getElementById('legend');
            legendDiv.innerHTML = '<h4>Legend</h4>'; // Reset legend content

            auctionData.forEach((trader, traderIndex) => {
                const points = [];
                const color = TRADER_COLORS[traderIndex % TRADER_COLORS.length];
                const lineMaterial = new THREE.LineBasicMaterial({ color: color, linewidth: 2 });
                const markerMaterial = new THREE.MeshBasicMaterial({ color: color });
                const markerGeometry = new THREE.SphereGeometry(MARKER_SIZE, 6, 6);

                trader.bids.forEach(bid => {
                    const x = bid.round * xScale;
                    const y = bid.quantity * yScale;
                    const z = (traderIndex * Z_SPACING_PER_TRADER_UNSCALED) * zScale;
                    const pointVec = new THREE.Vector3(x, y, z);
                    points.push(pointVec);
                    const marker = new THREE.Mesh(markerGeometry, markerMaterial);
                    marker.position.copy(pointVec);
                    traderObjectsGroup.add(marker);
                });
                if (points.length > 1) {
                    const lineGeometry = new THREE.BufferGeometry().setFromPoints(points);
                    const line = new THREE.Line(lineGeometry, lineMaterial);
                    traderObjectsGroup.add(line);
                }
                const legendItem = document.createElement('div');
                // Simple legend item for now, can be styled better
                const colorBox = document.createElement('span');
                colorBox.style.display = 'inline-block';
                colorBox.style.width = '12px';
                colorBox.style.height = '12px';
                colorBox.style.marginRight = '8px';
                colorBox.style.backgroundColor = `#${color.toString(16).padStart(6, '0')}`;
                colorBox.style.border = '1px solid #666';
                legendItem.appendChild(colorBox);
                legendItem.appendChild(document.createTextNode(trader.traderId));
                legendDiv.appendChild(legendItem);
            });
            scene.add(traderObjectsGroup);
        }

        function updateCameraAndControlsTarget() {
            controls.target.set(X_MAX_DATA / 2 * xScale, Y_MAX_DATA / 3 * yScale, Z_MAX_DATA_UNSCALED / 2 * zScale);
            controls.update();
        }

        function setupSliders() {
            const slidersConfig = {
                xScale: { el: document.getElementById('xScale'), valEl: document.getElementById('xScaleValue'), updateFn: val => xScale = val, isScale: true },
                yScale: { el: document.getElementById('yScale'), valEl: document.getElementById('yScaleValue'), updateFn: val => yScale = val, isScale: true },
                zScale: { el: document.getElementById('zScale'), valEl: document.getElementById('zScaleValue'), updateFn: val => zScale = val, isScale: true },
                minAzimuth: { el: document.getElementById('minAzimuth'), valEl: document.getElementById('minAzimuthValue'), updateFn: val => controls.minAzimuthAngle = val, formatFn: formatAngle },
                maxAzimuth: { el: document.getElementById('maxAzimuth'), valEl: document.getElementById('maxAzimuthValue'), updateFn: val => controls.maxAzimuthAngle = val, formatFn: formatAngle },
                minPolar: { el: document.getElementById('minPolar'), valEl: document.getElementById('minPolarValue'), updateFn: val => controls.minPolarAngle = val, formatFn: formatAngle },
                maxPolar: { el: document.getElementById('maxPolar'), valEl: document.getElementById('maxPolarValue'), updateFn: val => controls.maxPolarAngle = val, formatFn: formatAngle }
            };

            function formatAngle(valueStr) {
                const value = parseFloat(valueStr);
                if (Math.abs(value - Math.PI) < 0.015) return "π"; // Increased tolerance
                if (Math.abs(value + Math.PI) < 0.015) return "-π";
                if (Math.abs(value - Math.PI/2) < 0.015) return "π/2";
                if (Math.abs(value + Math.PI/2) < 0.015) return "-π/2";
                if (Math.abs(value) < 0.015) return "0";
                return value.toFixed(2);
            }

            Object.values(slidersConfig).forEach(config => {
                // Set initial JS variable from HTML slider value (which should be the default)
                config.updateFn(parseFloat(config.el.value));
                // Set initial display value
                config.valEl.textContent = config.formatFn ? config.formatFn(config.el.value) : parseFloat(config.el.value).toFixed(1);

                config.el.addEventListener('input', (event) => {
                    const value = parseFloat(event.target.value);
                    config.updateFn(value);
                    config.valEl.textContent = config.formatFn ? config.formatFn(value) : value.toFixed(1);

                    if (config.isScale) {
                        createAxesAndGrid();
                        plotData();
                        updateCameraAndControlsTarget();
                    } else {
                        controls.update(); // For orbit limits
                    }
                });
            });
            // Position sliders panel below legend
            const legendEl = document.getElementById('legend');
            const slidersEl = document.getElementById('slidersContainer');
            // Ensure legend is populated before getting its height
            requestAnimationFrame(() => { // Wait for next frame for layout to settle
                 if (legendEl && slidersEl) {
                    const legendRect = legendEl.getBoundingClientRect();
                    slidersEl.style.top = (legendEl.offsetTop + legendRect.height + 10) + 'px';
                }
            });
        }

        function init() {
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x111111);

            // xScale, yScale, zScale are already initialized with JS defaults
            // The HTML 'value' attributes should match these defaults.
            // setupSliders will read these HTML values to set the display and apply orbit limits.

            const aspect = window.innerWidth / window.innerHeight;
            camera = new THREE.PerspectiveCamera(45, aspect, 1, 2000);
            // Initial camera position uses the JS default scales
            camera.position.set(X_MAX_DATA * 1.5 * xScale, Y_MAX_DATA * 0.7 * yScale, (Z_MAX_DATA_UNSCALED + 40) * zScale);

            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            document.getElementById('container').appendChild(renderer.domElement);

            const ambientLight = new THREE.AmbientLight(0xffffff, 0.9);
            scene.add(ambientLight);
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.3);
            directionalLight.position.set(0.5, 1, 0.75).normalize();
            scene.add(directionalLight);

            controls = new OrbitControls(camera, renderer.domElement);
            controls.enablePan = true;
            updateCameraAndControlsTarget(); // Set initial target based on JS default scales
            controls.minDistance = 5; // Adjusted min distance
            controls.maxDistance = 1000;

            const fontLoader = new FontLoader();
            fontLoader.load('https://unpkg.com/three@0.158.0/examples/fonts/helvetiker_regular.typeface.json', function (font) {
                helvetikerFont = font;
                setupSliders(); // Setup sliders first: this will apply initial orbit limits and confirm scales
                createAxesAndGrid(); // Then draw based on current (initial) scales
                plotData();          // Plot data based on current (initial) scales
            });
            window.addEventListener('resize', onWindowResize);
        }
        function onWindowResize() { /* ... (same as before) ... */
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }
        function animate() { /* ... (same as before) ... */
            requestAnimationFrame(animate);
            controls.update();
            renderer.render(scene, camera);
        }
        init();
    </script>
</body>
</html>