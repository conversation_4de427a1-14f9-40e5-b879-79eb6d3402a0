<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Auction Clock Chart</title>
    <style>
        body { margin: 0; overflow: hidden; background-color: #1e1e1e; color: #e0e0e0; font-family: Arial, sans-serif; }
        canvas { display: block; }
        #legend {
            position: absolute;
            top: 20px;
            right: 20px;
            background-color: rgba(40, 40, 40, 0.85);
            padding: 10px 15px;
            border-radius: 5px;
            border: 1px solid #555;
        }
        #legend h4 {
            margin-top: 0;
            margin-bottom: 8px;
            font-size: 14px;
            border-bottom: 1px solid #555;
            padding-bottom: 5px;
        }
        #legend div {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            font-size: 12px;
        }
        #legend span.color-box {
            display: inline-block;
            width: 12px;
            height: 12px;
            margin-right: 8px;
            border: 1px solid #777;
        }
    </style>
</head>
<body>
    <div id="container"></div>
    <div id="legend">
        <h4>Legend</h4>
        <!-- Legend items will be added here by JavaScript -->
    </div>

    <script type="importmap">
        {
            "imports": {
                "three": "https://unpkg.com/three@0.158.0/build/three.module.js",
                "three/addons/": "https://unpkg.com/three@0.158.0/examples/jsm/"
            }
        }
    </script>

    <script type="module">
        import * as THREE from 'three';
        import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
        import { FontLoader } from 'three/addons/loaders/FontLoader.js';
        import { TextGeometry } from 'three/addons/geometries/TextGeometry.js';

        let scene, camera, renderer, controls;
        const traderObjectsGroup = new THREE.Group(); // Group for all lines and markers
        const axesGroup = new THREE.Group(); // Group for axes elements

        const NUM_TRADERS = 5;
        const NUM_ROUNDS = 10;
        const INITIAL_QUANTITY = 100;
        const Z_SPACING_PER_TRADER = 15; // How far apart traders are on Z-axis
        const Y_AXIS_MAX = INITIAL_QUANTITY;
        const X_AXIS_MAX = NUM_ROUNDS;
        const Z_AXIS_MAX = (NUM_TRADERS -1) * Z_SPACING_PER_TRADER;


        const TRADER_COLORS = [
            0xffde3d, // Yellowish
            0xff9234, // Orange
            0xff5733, // Reddish-Orange
            0xc70039, // Crimson
            0x900c3f  // Dark Magenta
        ];

        // --- Data Generation ---
        function generateAuctionData(numTraders, numRounds, initialQuantity) {
            const tradersData = [];
            for (let t = 0; t < numTraders; t++) {
                const trader = {
                    traderId: `Trader ${t + 1}`,
                    bids: []
                };
                let currentQuantity = initialQuantity;
                for (let r = 1; r <= numRounds; r++) {
                    if (r === 1) {
                        trader.bids.push({ round: r, quantity: initialQuantity });
                    } else {
                        if (currentQuantity > 0) {
                            const decrease = Math.floor(Math.random() * 5) + 1;
                            currentQuantity -= decrease;
                            currentQuantity = Math.max(0, currentQuantity);
                        }
                        trader.bids.push({ round: r, quantity: currentQuantity });
                    }
                }
                tradersData.push(trader);
            }
            return tradersData;
        }

        const auctionData = generateAuctionData(NUM_TRADERS, NUM_ROUNDS, INITIAL_QUANTITY);

        // --- Helper to create text (using TextGeometry for better quality) ---
        let helvetikerFont = null;
        function loadFontAndCreateText(text, position, color, size, rotation = new THREE.Euler(0,0,0), callback) {
            if (!helvetikerFont) {
                const loader = new FontLoader();
                loader.load('https://unpkg.com/three@0.158.0/examples/fonts/helvetiker_regular.typeface.json', function (font) {
                    helvetikerFont = font;
                    createTextInstance(text, position, color, size, rotation, callback);
                });
            } else {
                createTextInstance(text, position, color, size, rotation, callback);
            }
        }

        function createTextInstance(text, position, color, size, rotation, callback) {
            const textGeo = new TextGeometry(text, {
                font: helvetikerFont,
                size: size,
                height: size * 0.1, // depth of text
                curveSegments: 4,
            });
            textGeo.center(); // Center the geometry
            const textMat = new THREE.MeshBasicMaterial({ color: color });
            const textMesh = new THREE.Mesh(textGeo, textMat);
            textMesh.position.copy(position);
            textMesh.rotation.copy(rotation);
            if (callback) callback(textMesh);
        }


        // --- Create Axes and Grid ---
        function createAxesAndGrid() {
            axesGroup.clear();
            const axisLineMaterial = new THREE.LineBasicMaterial({ color: 0xaaaaaa });
            const gridColor = 0x444444;
            const divisions = 10;

            // Centering offset for the whole plot
            const offsetX = -X_AXIS_MAX / 2;
            const offsetY = -Y_AXIS_MAX / 2;
            const offsetZ = -Z_AXIS_MAX / 2;


            // X-Axis (Rounds)
            const xAxisGeo = new THREE.BufferGeometry().setFromPoints([
                new THREE.Vector3(offsetX, offsetY, offsetZ),
                new THREE.Vector3(X_AXIS_MAX + offsetX, offsetY, offsetZ)
            ]);
            axesGroup.add(new THREE.Line(xAxisGeo, axisLineMaterial));
            loadFontAndCreateText('Round', new THREE.Vector3(X_AXIS_MAX / 2 + offsetX, offsetY - 8, offsetZ), 0xffffff, 3, new THREE.Euler(0,0,0), (mesh) => axesGroup.add(mesh));
            for (let i = 0; i <= NUM_ROUNDS; i += 2) {
                if (i === 0 && NUM_ROUNDS > 0) continue; // Skip 0 if we have rounds
                 loadFontAndCreateText(i.toString(), new THREE.Vector3(i + offsetX, offsetY - 4, offsetZ), 0xdddddd, 2, new THREE.Euler(0,0,0), (mesh) => axesGroup.add(mesh));
            }


            // Y-Axis (Quantity)
            const yAxisGeo = new THREE.BufferGeometry().setFromPoints([
                new THREE.Vector3(offsetX, offsetY, offsetZ),
                new THREE.Vector3(offsetX, Y_AXIS_MAX + offsetY, offsetZ)
            ]);
            axesGroup.add(new THREE.Line(yAxisGeo, axisLineMaterial));
            loadFontAndCreateText('Quantity', new THREE.Vector3(offsetX - 8, Y_AXIS_MAX / 2 + offsetY, offsetZ), 0xffffff, 3, new THREE.Euler(0,0,Math.PI/2), (mesh) => axesGroup.add(mesh));
             for (let i = 0; i <= INITIAL_QUANTITY; i += 20) {
                 loadFontAndCreateText(i.toString(), new THREE.Vector3(offsetX - 4, i + offsetY, offsetZ), 0xdddddd, 2, new THREE.Euler(0,0,0), (mesh) => axesGroup.add(mesh));
            }

            // Z-Axis (Trader separation - conceptual, no line needed if traders are on Z)
            // We can add a label if desired
            loadFontAndCreateText('Trader', new THREE.Vector3(offsetX, offsetY, Z_AXIS_MAX / 2 + offsetZ), 0xffffff, 3, new THREE.Euler(0,Math.PI/2,0), (mesh) => axesGroup.add(mesh));


            // Grid
            const gridXZ = new THREE.GridHelper(Math.max(X_AXIS_MAX, Z_AXIS_MAX), divisions, gridColor, gridColor);
            gridXZ.position.set(X_AXIS_MAX/2 + offsetX, offsetY, Z_AXIS_MAX/2 + offsetZ);
            axesGroup.add(gridXZ);

            const gridXY = new THREE.GridHelper(Math.max(X_AXIS_MAX, Y_AXIS_MAX), divisions, gridColor, gridColor);
            gridXY.position.set(X_AXIS_MAX/2 + offsetX, Y_AXIS_MAX/2 + offsetY, offsetZ);
            gridXY.rotation.x = Math.PI / 2;
            axesGroup.add(gridXY);

            const gridYZ = new THREE.GridHelper(Math.max(Y_AXIS_MAX, Z_AXIS_MAX), divisions, gridColor, gridColor);
            gridYZ.position.set(offsetX, Y_AXIS_MAX/2 + offsetY, Z_AXIS_MAX/2 + offsetZ);
            gridYZ.rotation.z = Math.PI / 2;
            axesGroup.add(gridYZ);

            scene.add(axesGroup);
        }


        // --- Plot Trader Data ---
        function plotData() {
            traderObjectsGroup.clear(); // Clear previous lines/markers
            const legendDiv = document.getElementById('legend');
            legendDiv.innerHTML = '<h4>Legend</h4>'; // Reset legend

            // Centering offset for the whole plot
            const offsetX = -X_AXIS_MAX / 2;
            const offsetY = -Y_AXIS_MAX / 2;
            const offsetZ = -Z_AXIS_MAX / 2;

            auctionData.forEach((trader, traderIndex) => {
                const points = [];
                const color = TRADER_COLORS[traderIndex % TRADER_COLORS.length];
                const lineMaterial = new THREE.LineBasicMaterial({ color: color, linewidth: 2 }); // linewidth > 1 might not be consistent
                const markerMaterial = new THREE.MeshBasicMaterial({ color: color });
                const markerGeometry = new THREE.SphereGeometry(0.7, 8, 8); // Small spheres

                trader.bids.forEach(bid => {
                    const x = bid.round + offsetX;
                    const y = bid.quantity + offsetY;
                    const z = (traderIndex * Z_SPACING_PER_TRADER) + offsetZ;
                    const pointVec = new THREE.Vector3(x, y, z);
                    points.push(pointVec);

                    // Add marker
                    const marker = new THREE.Mesh(markerGeometry, markerMaterial);
                    marker.position.copy(pointVec);
                    traderObjectsGroup.add(marker);
                });

                const lineGeometry = new THREE.BufferGeometry().setFromPoints(points);
                const line = new THREE.Line(lineGeometry, lineMaterial);
                traderObjectsGroup.add(line);

                // Add to HTML Legend
                const legendItem = document.createElement('div');
                legendItem.innerHTML = `<span class="color-box" style="background-color: #${color.toString(16).padStart(6, '0')};"></span> ${trader.traderId}`;
                legendDiv.appendChild(legendItem);
            });
            scene.add(traderObjectsGroup);
        }


        function init() {
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x1e1e1e);

            const aspect = window.innerWidth / window.innerHeight;
            camera = new THREE.PerspectiveCamera(50, aspect, 0.1, 2000);
            // Adjust camera position to view the data range
            // X: 0-10, Y: 0-100, Z: 0 to (NUM_TRADERS-1)*Z_SPACING_PER_TRADER
            camera.position.set(X_AXIS_MAX * 1.5, Y_AXIS_MAX * 0.75, Z_AXIS_MAX * 2.5);
            camera.lookAt(0, Y_AXIS_MAX / 3, 0); // Look towards the center of the data mass

            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            document.getElementById('container').appendChild(renderer.domElement);

            const ambientLight = new THREE.AmbientLight(0xffffff, 0.7);
            scene.add(ambientLight);
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.5);
            directionalLight.position.set(1, 1, 1).normalize();
            scene.add(directionalLight);

            controls = new OrbitControls(camera, renderer.domElement);
            controls.target.set(0, Y_AXIS_MAX / 3, Z_AXIS_MAX / 3); // Adjust target for better rotation center
            controls.update();


            // Load font first, then create axes and plot data
            const fontLoader = new FontLoader();
            fontLoader.load('https://unpkg.com/three@0.158.0/examples/fonts/helvetiker_regular.typeface.json', function (font) {
                helvetikerFont = font; // Make font globally available for text creation
                createAxesAndGrid();
                plotData();
            });


            window.addEventListener('resize', onWindowResize, false);
        }

        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }

        function animate() {
            requestAnimationFrame(animate);
            controls.update();
            renderer.render(scene, camera);
        }

        init();
        animate();
    </script>
</body>
</html>