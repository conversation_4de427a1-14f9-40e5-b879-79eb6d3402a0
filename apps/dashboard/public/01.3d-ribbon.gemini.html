<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Three.js 3D Line Chart</title>
    <style>
        body { margin: 0; overflow: hidden; background-color: #282c34; }
        canvas { display: block; }
    </style>
</head>
<body>
    <div id="container"></div>

    <script type="importmap">
        {
            "imports": {
                "three": "https://unpkg.com/three@0.158.0/build/three.module.js",
                "three/addons/": "https://unpkg.com/three@0.158.0/examples/jsm/"
            }
        }
    </script>

    <script type="module">
        import * as THREE from 'three';
        import { OrbitControls } from 'three/addons/controls/OrbitControls.js';

        let scene, camera, renderer, controls;
        let line1, line2, line3;
        const axesGroup = new THREE.Group(); // To group axes elements

        // --- Data Generation (same as your R3F code) ---
        function generateSpiralData(offsetY = 0, twists = 5, radius = 5, points = 200, height = 10) {
            const data = [];
            for (let i = 0; i <= points; i++) {
                const t = (i / points) * Math.PI * 2 * twists;
                const x = Math.cos(t) * radius;
                const z = Math.sin(t) * radius;
                const y = (i / points) * height - height / 2 + offsetY;
                data.push(new THREE.Vector3(x, y, z));
            }
            return data;
        }

        const line1Points = generateSpiralData(0, 5, 5, 200, 10);
        const line2Points = generateSpiralData(2, 3, 4, 150, 8);
        const line3Points = (() => {
            const points = [];
            for (let i = 0; i < 100; i++) {
                points.push(new THREE.Vector3(
                    Math.sin(i * 0.1) * 6,
                    Math.cos(i * 0.15) * 3 + i * 0.05 - 2.5,
                    Math.sin(i * 0.05) * 5
                ));
            }
            return points;
        })();

        // --- Helper to create a line ---
        function createLine(pointsArray, color, lineWidth) {
            const geometry = new THREE.BufferGeometry().setFromPoints(pointsArray);
            const material = new THREE.LineBasicMaterial({ color: color, linewidth: lineWidth });
            // Note: linewidth > 1 is not guaranteed to work on all platforms/drivers
            // for LineBasicMaterial. For thicker lines with better support,
            // you'd use LineMaterial from 'three/addons/lines/LineMaterial.js'
            // which requires LineGeometry and Line2.
            const line = new THREE.Line(geometry, material);
            return line;
        }

        // --- Helper to create text sprite ---
        function createTextSprite(text, position, color, fontSize = 32, fontFace = 'Arial') {
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');
            const font = `${fontSize}px ${fontFace}`;
            context.font = font;
            const metrics = context.measureText(text);
            const textWidth = metrics.width;

            // A bit of padding
            canvas.width = textWidth + fontSize * 0.2;
            canvas.height = fontSize * 1.2;
            context.font = font; // Re-set font after canvas resize
            context.fillStyle = color;
            context.textAlign = 'center';
            context.textBaseline = 'middle';
            context.fillText(text, canvas.width / 2, canvas.height / 2);

            const texture = new THREE.CanvasTexture(canvas);
            const spriteMaterial = new THREE.SpriteMaterial({ map: texture });
            const sprite = new THREE.Sprite(spriteMaterial);

            // Adjust scale to make text look reasonably sized
            // This is empirical and might need adjustment based on your scene scale
            const spriteScale = 0.02 * fontSize;
            sprite.scale.set(spriteScale * (canvas.width / canvas.height), spriteScale, 1);
            sprite.position.copy(position);

            return sprite;
        }

        // --- Create Axes ---
        function createAxes(size = 10) {
            axesGroup.clear(); // Clear previous axes if any

            // X Axis
            const xAxisLine = createLine([new THREE.Vector3(0,0,0), new THREE.Vector3(size,0,0)], 0xff0000, 2);
            const xLabel = createTextSprite('X', new THREE.Vector3(size + 0.5, 0, 0), 'red', 48);
            axesGroup.add(xAxisLine, xLabel);

            // Y Axis
            const yAxisLine = createLine([new THREE.Vector3(0,0,0), new THREE.Vector3(0,size,0)], 0x00ff00, 2);
            const yLabel = createTextSprite('Y', new THREE.Vector3(0, size + 0.5, 0), 'green', 48);
            axesGroup.add(yAxisLine, yLabel);

            // Z Axis
            const zAxisLine = createLine([new THREE.Vector3(0,0,0), new THREE.Vector3(0,0,size)], 0x0000ff, 2);
            const zLabel = createTextSprite('Z', new THREE.Vector3(0, 0, size + 0.5), 'blue', 48);
            axesGroup.add(zAxisLine, zLabel);

            scene.add(axesGroup);
        }


        function init() {
            // Scene
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x282c34); // Match R3F example background

            // Camera
            const aspect = window.innerWidth / window.innerHeight;
            camera = new THREE.PerspectiveCamera(75, aspect, 0.1, 1000);
            camera.position.set(15, 15, 15);
            camera.lookAt(scene.position);

            // Renderer
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            document.getElementById('container').appendChild(renderer.domElement);

            // Lights
            const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
            scene.add(ambientLight);

            const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
            directionalLight.position.set(10, 10, 5);
            scene.add(directionalLight);

            // Controls
            controls = new OrbitControls(camera, renderer.domElement);
            controls.enablePan = true;
            controls.enableZoom = true;
            controls.enableRotate = true;
            // controls.update() must be called after any manual changes to the camera's transform
            // or if controls.enableDamping or controls.autoRotate are set to true.
            // We'll call it in the animate loop.

            // Create Line Geometries
            line1 = createLine(line1Points, 0xff69b4, 3); // hotpink
            line2 = createLine(line2Points, 0x00ffff, 3); // cyan
            line3 = createLine(line3Points, 0xffff00, 3); // yellow

            scene.add(line1);
            scene.add(line2);
            scene.add(line3);

            // Create Axes
            createAxes(10); // Axes size

            // Optional: Grid Helper
            const gridHelper = new THREE.GridHelper(20, 20);
            scene.add(gridHelper);


            // Handle window resize
            window.addEventListener('resize', onWindowResize, false);
        }

        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }

        function animate() {
            requestAnimationFrame(animate);
            controls.update(); // only required if controls.enableDamping or controls.autoRotate are set to true
            render();
        }

        function render() {
            renderer.render(scene, camera);
        }

        // Start everything
        init();
        animate();

    </script>
</body>
</html>
