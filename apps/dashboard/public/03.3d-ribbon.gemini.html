<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Auction Clock Chart</title>
    <style>
        body { margin: 0; overflow: hidden; background-color: #111; /* Darker background */ color: #e0e0e0; font-family: Arial, sans-serif; }
        canvas { display: block; }
        #legend {
            position: absolute;
            top: 20px;
            right: 20px;
            background-color: rgba(30, 30, 30, 0.85); /* Darker legend */
            padding: 10px 15px;
            border-radius: 5px;
            border: 1px solid #444;
        }
        #legend h4 {
            margin-top: 0;
            margin-bottom: 8px;
            font-size: 14px;
            border-bottom: 1px solid #444;
            padding-bottom: 5px;
        }
        #legend div {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            font-size: 12px;
        }
        #legend span.color-box {
            display: inline-block;
            width: 12px;
            height: 12px;
            margin-right: 8px;
            border: 1px solid #666;
        }
    </style>
</head>
<body>
    <div id="container"></div>
    <div id="legend">
        <h4>Legend</h4>
        <!-- Legend items will be added here by JavaScript -->
    </div>

    <script type="importmap">
        {
            "imports": {
                "three": "https://unpkg.com/three@0.158.0/build/three.module.js",
                "three/addons/": "https://unpkg.com/three@0.158.0/examples/jsm/"
            }
        }
    </script>

    <script type="module">
        import * as THREE from 'three';
        import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
        import { FontLoader } from 'three/addons/loaders/FontLoader.js';
        import { TextGeometry } from 'three/addons/geometries/TextGeometry.js';

        let scene, camera, renderer, controls;
        const traderObjectsGroup = new THREE.Group();
        const axesGroup = new THREE.Group();

        const NUM_TRADERS = 5;
        const NUM_ROUNDS = 10;
        const INITIAL_QUANTITY = 100;
        const Z_SPACING_PER_TRADER = 8; // Closer spacing for traders
        const MARKER_SIZE = 0.4;

        // Max coordinates for data plotting
        const X_MAX_DATA = NUM_ROUNDS;
        const Y_MAX_DATA = INITIAL_QUANTITY;
        const Z_MAX_DATA = (NUM_TRADERS - 1) * Z_SPACING_PER_TRADER;

        const TRADER_COLORS = [
            0xffde3d, // Yellow
            0xffb732, // Light Orange
            0xff9234, // Orange
            0xff6d3a, // Reddish-Orange
            0xff4530  // Red
        ]; // Colors from yellow to red

        let helvetikerFont = null;

        function generateAuctionData(numTraders, numRounds, initialQuantity) {
            const tradersData = [];
            for (let t = 0; t < numTraders; t++) {
                const trader = { traderId: `Trader ${t + 1}`, bids: [] };
                let currentQuantity = initialQuantity;
                for (let r = 1; r <= numRounds; r++) {
                    if (r === 1) {
                        trader.bids.push({ round: r, quantity: initialQuantity });
                    } else {
                        if (currentQuantity > 0) {
                            const decrease = Math.floor(Math.random() * 5) + 1;
                            currentQuantity = Math.max(0, currentQuantity - decrease);
                        }
                        trader.bids.push({ round: r, quantity: currentQuantity });
                    }
                }
                tradersData.push(trader);
            }
            return tradersData;
        }

        const auctionData = generateAuctionData(NUM_TRADERS, NUM_ROUNDS, INITIAL_QUANTITY);

        function createText(text, position, color, size, rotation = new THREE.Euler(0,0,0), callback) {
            if (!helvetikerFont) {
                console.error("Font not loaded yet for creating text:", text);
                return;
            }
            const textGeo = new TextGeometry(text, {
                font: helvetikerFont, size: size, height: size * 0.1, curveSegments: 4,
            });
            textGeo.center();
            const textMat = new THREE.MeshBasicMaterial({ color: color });
            const textMesh = new THREE.Mesh(textGeo, textMat);
            textMesh.position.copy(position);
            textMesh.rotation.copy(rotation);
            if (callback) callback(textMesh);
            else axesGroup.add(textMesh); // Default add to axesGroup
        }

        function createAxesAndGrid() {
            axesGroup.clear();
            const axisLineMaterial = new THREE.LineBasicMaterial({ color: 0x999999 });
            const gridColor = 0x555555; // Slightly lighter grid
            const tickColor = 0xcccccc;
            const labelColor = 0xffffff;
            const axisLabelSize = 2.0;
            const tickLabelSize = 1.5;

            // Axis lines (starting from origin)
            axesGroup.add(new THREE.Line(new THREE.BufferGeometry().setFromPoints([new THREE.Vector3(0,0,0), new THREE.Vector3(X_MAX_DATA + 2, 0, 0)]), axisLineMaterial)); // X
            axesGroup.add(new THREE.Line(new THREE.BufferGeometry().setFromPoints([new THREE.Vector3(0,0,0), new THREE.Vector3(0, Y_MAX_DATA + 5, 0)]), axisLineMaterial)); // Y
            axesGroup.add(new THREE.Line(new THREE.BufferGeometry().setFromPoints([new THREE.Vector3(0,0,0), new THREE.Vector3(0, 0, Z_MAX_DATA + Z_SPACING_PER_TRADER/2)]), axisLineMaterial)); // Z

            // Labels
            createText('Round', new THREE.Vector3(X_MAX_DATA / 2, -axisLabelSize*2, 0), labelColor, axisLabelSize);
            createText('Quantity', new THREE.Vector3(-axisLabelSize*2.5, Y_MAX_DATA / 2, 0), labelColor, axisLabelSize, new THREE.Euler(0,0,Math.PI/2));
            createText('Trader', new THREE.Vector3(0, -axisLabelSize*2, Z_MAX_DATA / 2), labelColor, axisLabelSize, new THREE.Euler(0,Math.PI/2,0));

            // Ticks
            for (let i = 2; i <= X_MAX_DATA; i += 2) {
                createText(i.toString(), new THREE.Vector3(i, -tickLabelSize*1.5, 0), tickColor, tickLabelSize);
            }
            for (let i = 20; i <= Y_MAX_DATA; i += 20) {
                createText(i.toString(), new THREE.Vector3(-tickLabelSize, i, 0), tickColor, tickLabelSize);
            }
            // No numerical Z ticks for traders, they are distinct series

            // Grid
            const gridXZ = new THREE.GridHelper(Math.max(X_MAX_DATA, Z_MAX_DATA) + Z_SPACING_PER_TRADER, NUM_ROUNDS, gridColor, gridColor);
            gridXZ.position.set(X_MAX_DATA / 2, 0, Z_MAX_DATA / 2);
            axesGroup.add(gridXZ);

            const gridXY = new THREE.GridHelper(Math.max(X_MAX_DATA, Y_MAX_DATA), NUM_ROUNDS, gridColor, gridColor);
            gridXY.position.set(X_MAX_DATA / 2, Y_MAX_DATA / 2, 0);
            gridXY.rotation.x = Math.PI / 2;
            axesGroup.add(gridXY);

            const gridYZ = new THREE.GridHelper(Math.max(Y_MAX_DATA, Z_MAX_DATA) + Z_SPACING_PER_TRADER, Y_MAX_DATA/10, gridColor, gridColor); // Y divisions by 10
            gridYZ.position.set(0, Y_MAX_DATA / 2, Z_MAX_DATA / 2);
            gridYZ.rotation.z = Math.PI / 2;
            axesGroup.add(gridYZ);

            scene.add(axesGroup);
        }

        function plotData() {
            traderObjectsGroup.clear();
            const legendDiv = document.getElementById('legend');
            legendDiv.innerHTML = '<h4>Legend</h4>';

            auctionData.forEach((trader, traderIndex) => {
                const points = [];
                const color = TRADER_COLORS[traderIndex % TRADER_COLORS.length];
                const lineMaterial = new THREE.LineBasicMaterial({ color: color, linewidth: 2 });
                const markerMaterial = new THREE.MeshBasicMaterial({ color: color });
                const markerGeometry = new THREE.SphereGeometry(MARKER_SIZE, 6, 6);

                trader.bids.forEach(bid => {
                    const x = bid.round; // Rounds 1-10
                    const y = bid.quantity;
                    const z = traderIndex * Z_SPACING_PER_TRADER;
                    const pointVec = new THREE.Vector3(x, y, z);
                    points.push(pointVec);

                    const marker = new THREE.Mesh(markerGeometry, markerMaterial);
                    marker.position.copy(pointVec);
                    traderObjectsGroup.add(marker);
                });

                const lineGeometry = new THREE.BufferGeometry().setFromPoints(points);
                const line = new THREE.Line(lineGeometry, lineMaterial);
                traderObjectsGroup.add(line);

                const legendItem = document.createElement('div');
                legendItem.innerHTML = `<span class="color-box" style="background-color: #${color.toString(16).padStart(6, '0')};"></span> ${trader.traderId}`;
                legendDiv.appendChild(legendItem);
            });
            scene.add(traderObjectsGroup);
        }

        function init() {
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x111111); // Darker background

            const aspect = window.innerWidth / window.innerHeight;
            camera = new THREE.PerspectiveCamera(50, aspect, 0.1, 2000);
            // Adjusted camera position for a view similar to the image
            camera.position.set(X_MAX_DATA * 1.8, Y_MAX_DATA * 0.6, Z_MAX_DATA * 1.5 + 20);


            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            document.getElementById('container').appendChild(renderer.domElement);

            const ambientLight = new THREE.AmbientLight(0xffffff, 0.8);
            scene.add(ambientLight);
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.4);
            directionalLight.position.set(1, 1.5, 1).normalize();
            scene.add(directionalLight);

            controls = new OrbitControls(camera, renderer.domElement);
            controls.enablePan = true; // Panning is enabled
            // Adjust target to be center of data mass for better rotation
            controls.target.set(X_MAX_DATA / 2, Y_MAX_DATA / 3, Z_MAX_DATA / 2);
            controls.update();

            const fontLoader = new FontLoader();
            fontLoader.load('https://unpkg.com/three@0.158.0/examples/fonts/helvetiker_regular.typeface.json', function (font) {
                helvetikerFont = font;
                createAxesAndGrid();
                plotData();
            });

            window.addEventListener('resize', onWindowResize, false);
        }

        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }

        function animate() {
            requestAnimationFrame(animate);
            controls.update();
            renderer.render(scene, camera);
        }

        init();
        animate();
    </script>
</body>
</html>