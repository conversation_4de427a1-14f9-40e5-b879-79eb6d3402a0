<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Auction Clock Chart</title>
    <style>
        body { margin: 0; overflow: hidden; background-color: #111; color: #e0e0e0; font-family: Arial, sans-serif; }
        canvas { display: block; }
        #legend {
            position: absolute;
            top: 20px;
            right: 20px;
            background-color: rgba(30, 30, 30, 0.85);
            padding: 10px 15px;
            border-radius: 5px;
            border: 1px solid #444;
        }
        #legend h4 { margin-top: 0; margin-bottom: 8px; font-size: 14px; border-bottom: 1px solid #444; padding-bottom: 5px; }
        #legend div { display: flex; align-items: center; margin-bottom: 5px; font-size: 12px; }
        #legend span.color-box { display: inline-block; width: 12px; height: 12px; margin-right: 8px; border: 1px solid #666; }
    </style>
</head>
<body>
    <div id="container"></div>
    <div id="legend"><h4>Legend</h4></div>

    <script type="importmap">
        {
            "imports": {
                "three": "https://unpkg.com/three@0.158.0/build/three.module.js",
                "three/addons/": "https://unpkg.com/three@0.158.0/examples/jsm/"
            }
        }
    </script>

    <script type="module">
        import * as THREE from 'three';
        import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
        import { FontLoader } from 'three/addons/loaders/FontLoader.js';
        import { TextGeometry } from 'three/addons/geometries/TextGeometry.js';

        let scene, camera, renderer, controls;
        const traderObjectsGroup = new THREE.Group();
        const axesGroup = new THREE.Group();

        const NUM_TRADERS = 5;
        const NUM_ROUNDS = 10;
        const INITIAL_QUANTITY = 100;
        const Z_SPACING_PER_TRADER = 8;
        const MARKER_SIZE = 0.4;

        const X_MAX_DATA = NUM_ROUNDS;
        const Y_MAX_DATA = INITIAL_QUANTITY;
        const Z_MAX_DATA = (NUM_TRADERS > 0 ? (NUM_TRADERS - 1) : 0) * Z_SPACING_PER_TRADER;


        const TRADER_COLORS = [0xffde3d, 0xffb732, 0xff9234, 0xff6d3a, 0xff4530];
        let helvetikerFont = null;

        function generateAuctionData(numTraders, numRounds, initialQuantity) {
            const tradersData = [];
            for (let t = 0; t < numTraders; t++) {
                const trader = { traderId: `Trader ${t + 1}`, bids: [] };
                let currentQuantity = initialQuantity;
                for (let r = 1; r <= numRounds; r++) {
                    if (r === 1) trader.bids.push({ round: r, quantity: initialQuantity });
                    else {
                        if (currentQuantity > 0) {
                            const decrease = Math.floor(Math.random() * 5) + 1;
                            currentQuantity = Math.max(0, currentQuantity - decrease);
                        }
                        trader.bids.push({ round: r, quantity: currentQuantity });
                    }
                }
                tradersData.push(trader);
            }
            return tradersData;
        }
        const auctionData = generateAuctionData(NUM_TRADERS, NUM_ROUNDS, INITIAL_QUANTITY);

        function createText(text, position, color, size, rotation = new THREE.Euler(0,0,0), parent = axesGroup) {
            if (!helvetikerFont) { console.error("Font not loaded for text:", text); return; }
            const textGeo = new TextGeometry(text, { font: helvetikerFont, size: size, height: size * 0.05, curveSegments: 3 });
            textGeo.center();
            const textMat = new THREE.MeshBasicMaterial({ color: color });
            const textMesh = new THREE.Mesh(textGeo, textMat);
            textMesh.position.copy(position);
            textMesh.rotation.copy(rotation);
            parent.add(textMesh);
        }

        function createAxesAndGrid() {
            axesGroup.clear();
            const axisColor = 0x999999;
            const gridColor = 0x444444; // Darker grid lines
            const tickColor = 0xcccccc;
            const labelColor = 0xffffff;
            const axisLabelSize = 2.0;
            const tickLabelSize = 1.5;
            const axisLineMaterial = new THREE.LineBasicMaterial({ color: axisColor });
            const gridLineMaterial = new THREE.LineBasicMaterial({ color: gridColor });

            // Axis lines (starting from origin)
            axesGroup.add(new THREE.Line(new THREE.BufferGeometry().setFromPoints([new THREE.Vector3(0,0,0), new THREE.Vector3(X_MAX_DATA + 1, 0, 0)]), axisLineMaterial)); // X
            axesGroup.add(new THREE.Line(new THREE.BufferGeometry().setFromPoints([new THREE.Vector3(0,0,0), new THREE.Vector3(0, Y_MAX_DATA + 5, 0)]), axisLineMaterial)); // Y
            axesGroup.add(new THREE.Line(new THREE.BufferGeometry().setFromPoints([new THREE.Vector3(0,0,0), new THREE.Vector3(0, 0, Z_MAX_DATA + Z_SPACING_PER_TRADER * 0.5)]), axisLineMaterial)); // Z

            // Labels
            createText('Round', new THREE.Vector3(X_MAX_DATA / 2, -axisLabelSize*2.5, 0), labelColor, axisLabelSize);
            createText('Quantity', new THREE.Vector3(-axisLabelSize*3, Y_MAX_DATA / 2, 0), labelColor, axisLabelSize, new THREE.Euler(0,0,Math.PI/2));
            if (NUM_TRADERS > 1) { // Only show Trader label if multiple traders
                 createText('Trader', new THREE.Vector3(0, -axisLabelSize*2.5, Z_MAX_DATA / 2), labelColor, axisLabelSize, new THREE.Euler(0,Math.PI/2,0));
            }

            // Ticks for Y-Axis (Quantity)
            for (let i = 0; i <= Y_MAX_DATA; i += 20) { // Every 20 units for quantity
                if (i === 0 && Y_MAX_DATA > 0) continue; // Skip 0 tick if axis starts at 0
                createText(i.toString(), new THREE.Vector3(-tickLabelSize*0.7, i, 0), tickColor, tickLabelSize);
                 // Add small tick line
                const tickLineGeo = new THREE.BufferGeometry().setFromPoints([new THREE.Vector3(-0.5,i,0), new THREE.Vector3(0.5,i,0)]);
                axesGroup.add(new THREE.LineSegments(tickLineGeo, new THREE.LineBasicMaterial({color: axisColor})));
            }
             // Ticks for X-Axis (Round) - subtle, implied by grid mostly
            for (let i = 0; i <= X_MAX_DATA; i += 2) {
                if (i === 0 && X_MAX_DATA > 0) continue;
                createText(i.toString(), new THREE.Vector3(i, -tickLabelSize*1.2, 0), tickColor, tickLabelSize*0.8);
            }


            // --- Manual Grid Drawing ---
            const yGridStep = 10; // Grid line every 10 units of quantity

            // XZ Plane (Y=0, "floor")
            for (let i = 0; i <= X_MAX_DATA; i++) { // Lines parallel to Z, for each round
                const points = [new THREE.Vector3(i, 0, 0), new THREE.Vector3(i, 0, Z_MAX_DATA)];
                axesGroup.add(new THREE.LineSegments(new THREE.BufferGeometry().setFromPoints(points), gridLineMaterial));
            }
            for (let i = 0; i <= NUM_TRADERS -1 ; i++) { // Lines parallel to X, for each trader slot
                const zPos = i * Z_SPACING_PER_TRADER;
                const points = [new THREE.Vector3(0, 0, zPos), new THREE.Vector3(X_MAX_DATA, 0, zPos)];
                axesGroup.add(new THREE.LineSegments(new THREE.BufferGeometry().setFromPoints(points), gridLineMaterial));
            }
             if (NUM_TRADERS === 0 && Z_MAX_DATA === 0) { // Special case for no traders, draw Z=0 line
                const points = [new THREE.Vector3(0, 0, 0), new THREE.Vector3(X_MAX_DATA, 0, 0)];
                axesGroup.add(new THREE.LineSegments(new THREE.BufferGeometry().setFromPoints(points), gridLineMaterial));
            }


            // XY Plane (Z=0, "back wall")
            for (let i = 0; i <= X_MAX_DATA; i++) { // Lines parallel to Y, for each round
                const points = [new THREE.Vector3(i, 0, 0), new THREE.Vector3(i, Y_MAX_DATA, 0)];
                axesGroup.add(new THREE.LineSegments(new THREE.BufferGeometry().setFromPoints(points), gridLineMaterial));
            }
            for (let i = 0; i <= Y_MAX_DATA; i += yGridStep) { // Lines parallel to X, for quantity steps
                const points = [new THREE.Vector3(0, i, 0), new THREE.Vector3(X_MAX_DATA, i, 0)];
                axesGroup.add(new THREE.LineSegments(new THREE.BufferGeometry().setFromPoints(points), gridLineMaterial));
            }

            // YZ Plane (X=0, "side wall")
            for (let i = 0; i <= NUM_TRADERS -1; i++) { // Lines parallel to Y, for each trader slot
                const zPos = i * Z_SPACING_PER_TRADER;
                const points = [new THREE.Vector3(0, 0, zPos), new THREE.Vector3(0, Y_MAX_DATA, zPos)];
                axesGroup.add(new THREE.LineSegments(new THREE.BufferGeometry().setFromPoints(points), gridLineMaterial));
            }
             if (NUM_TRADERS === 0 && Z_MAX_DATA === 0) { // Special case for no traders, draw Z=0 line
                const points = [new THREE.Vector3(0, 0, 0), new THREE.Vector3(0, Y_MAX_DATA, 0)];
                axesGroup.add(new THREE.LineSegments(new THREE.BufferGeometry().setFromPoints(points), gridLineMaterial));
            }
            for (let i = 0; i <= Y_MAX_DATA; i += yGridStep) { // Lines parallel to Z, for quantity steps
                const points = [new THREE.Vector3(0, i, 0), new THREE.Vector3(0, i, Z_MAX_DATA)];
                axesGroup.add(new THREE.LineSegments(new THREE.BufferGeometry().setFromPoints(points), gridLineMaterial));
            }
            scene.add(axesGroup);
        }


        function plotData() {
            traderObjectsGroup.clear();
            const legendDiv = document.getElementById('legend');
            legendDiv.innerHTML = '<h4>Legend</h4>';

            auctionData.forEach((trader, traderIndex) => {
                const points = [];
                const color = TRADER_COLORS[traderIndex % TRADER_COLORS.length];
                const lineMaterial = new THREE.LineBasicMaterial({ color: color, linewidth: 2 });
                const markerMaterial = new THREE.MeshBasicMaterial({ color: color });
                const markerGeometry = new THREE.SphereGeometry(MARKER_SIZE, 6, 6);

                trader.bids.forEach(bid => {
                    const x = bid.round;
                    const y = bid.quantity;
                    const z = traderIndex * Z_SPACING_PER_TRADER;
                    const pointVec = new THREE.Vector3(x, y, z);
                    points.push(pointVec);
                    const marker = new THREE.Mesh(markerGeometry, markerMaterial);
                    marker.position.copy(pointVec);
                    traderObjectsGroup.add(marker);
                });
                if (points.length > 1) { // Only draw line if there are multiple points
                    const lineGeometry = new THREE.BufferGeometry().setFromPoints(points);
                    const line = new THREE.Line(lineGeometry, lineMaterial);
                    traderObjectsGroup.add(line);
                } else if (points.length === 1) { // If only one point, it's already added as a marker
                    // console.log("Trader has only one data point, marker added.");
                }


                const legendItem = document.createElement('div');
                legendItem.innerHTML = `<span class="color-box" style="background-color: #${color.toString(16).padStart(6, '0')};"></span> ${trader.traderId}`;
                legendDiv.appendChild(legendItem);
            });
            scene.add(traderObjectsGroup);
        }

        function init() {
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x111111);

            const aspect = window.innerWidth / window.innerHeight;
            camera = new THREE.PerspectiveCamera(45, aspect, 1, 2000); // Slightly narrower FOV
            camera.position.set(X_MAX_DATA * 1.5, Y_MAX_DATA * 0.7, Z_MAX_DATA + 40); // Adjusted Z for better initial view

            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            document.getElementById('container').appendChild(renderer.domElement);

            const ambientLight = new THREE.AmbientLight(0xffffff, 0.9); // Brighter ambient
            scene.add(ambientLight);
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.3);
            directionalLight.position.set(0.5, 1, 0.75).normalize();
            scene.add(directionalLight);

            controls = new OrbitControls(camera, renderer.domElement);
            controls.enablePan = true;
            controls.target.set(X_MAX_DATA / 2, Y_MAX_DATA / 3, Z_MAX_DATA / 2);
            controls.minDistance = 10;
            controls.maxDistance = 500;
            controls.update();

            const fontLoader = new FontLoader();
            fontLoader.load('https://unpkg.com/three@0.158.0/examples/fonts/helvetiker_regular.typeface.json', function (font) {
                helvetikerFont = font;
                createAxesAndGrid();
                plotData();
            });
            window.addEventListener('resize', onWindowResize, false);
        }
        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }
        function animate() {
            requestAnimationFrame(animate);
            controls.update();
            renderer.render(scene, camera);
        }
        init();
        animate();
    </script>
</body>
</html>