{"name": "dashboard", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build --docs", "preview-storybook": "serve storybook-static", "cosmos": "cosmos", "cosmos-export": "cosmos-export"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@nivo/sankey": "^0.99.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@react-three/drei": "^10.1.2", "@react-three/fiber": "^9.1.2", "@redux-devtools/extension": "^3.3.0", "@types/chroma-js": "^3.1.1", "@visx/gradient": "^3.12.0", "@visx/sankey": "^3.12.0", "@visx/scale": "^3.12.0", "ag-grid-community": "^33.3.0", "ag-grid-react": "^33.3.0", "browser-dtector": "^4.1.0", "chroma-js": "^3.1.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "comlink": "^4.4.2", "d3-array": "^3.2.4", "d3-sankey": "^0.12.3", "d3-scale": "^4.0.2", "d3-selection": "^3.0.0", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.15.0", "input-otp": "^1.4.2", "lodash": "^4.17.21", "lucide-react": "^0.511.0", "next-themes": "^0.4.6", "pako": "^2.1.0", "plotly.js-dist": "^3.0.1", "react": "^18.3.1", "react-day-picker": "8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.56.4", "react-plotly.js": "^2.6.0", "react-resizable-panels": "^3.0.2", "react-vis": "^1.12.1", "reaviz": "^16.0.4", "recharts": "^2.15.3", "sonner": "^2.0.4", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "three": "^0.176.0", "tsx": "^4.19.4", "uuid": "^11.1.0", "valtio": "^2.1.5", "vaul": "^1.1.2", "zod": "^3.25.45"}, "devDependencies": {"@playwright/test": "^1.52.0", "@react-buddy/ide-toolbox": "^2.4.0", "@storybook/addon-actions": "^8.6.14", "@storybook/addon-essentials": "^8.6.14", "@storybook/addon-links": "^8.6.14", "@storybook/react": "^8.6.14", "@storybook/react-vite": "^8.6.14", "@types/lodash": "^4.17.17", "@types/node": "^22.15.21", "@types/pako": "^2.0.3", "@types/plotly.js": "^3.0.0", "@types/react": "^18.3.22", "@types/react-dom": "^18.3.7", "@types/react-plotly.js": "^2.6.3", "@types/three": "^0.176.0", "@types/uuid": "^10.0.0", "@types/ws": "^8.18.1", "@vitejs/plugin-react": "^4.5.0", "@vitest/browser": "^3.1.4", "@vitest/coverage-istanbul": "^3.1.4", "@vitest/coverage-v8": "^3.1.4", "autoprefixer": "^10.4.21", "eslint": "^9.27.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "postcss": "^8.5.3", "react-cosmos": "^6.2.3", "react-cosmos-plugin-boolean-input": "^6.2.3", "react-cosmos-plugin-open-fixture": "^6.2.3", "react-cosmos-plugin-vite": "^6.2.0", "serve": "^14.2.4", "storybook": "^8.6.14", "tailwindcss": "^3.4.0", "typescript": "5.8.3", "vite": "^6.3.5", "vite-plugin-dts": "^4.5.4", "vitest": "^3.1.4", "ws": "^8.18.2"}}