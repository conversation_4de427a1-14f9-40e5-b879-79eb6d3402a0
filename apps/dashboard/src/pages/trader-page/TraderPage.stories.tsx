import type { Meta, StoryObj } from '@storybook/react';
import { TraderPage } from './TraderPage';

const meta: Meta<typeof TraderPage> = {
  title: 'Pages/TraderPage',
  component: TraderPage,
  parameters: {
    // Optional Storybook parameters
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
  argTypes: {
    // If you have specific controls for props, define them here
    // className: { control: 'text' },
    // style: { control: 'object' },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Default story for the TraderPage
export const Default: Story = {
  args: {
    // Props for the TraderPage can be set here if needed
    // className: '',
    // style: {},
  },
};

// You can add more stories here to showcase different states or variants
// export const AnotherState: Story = {
//   args: {
//     ...
//   },
// };