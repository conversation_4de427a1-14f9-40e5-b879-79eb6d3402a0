import React from 'react';
import { TraderPage } from './TraderPage';

/**
 * TraderPageDemo - Demo wrapper for the TraderPage component
 * 
 * This demo page showcases the full trader interface with:
 * - Auction name display
 * - Trader status widget with countdown timer
 * - Order entry form for buy/sell orders
 * - Trading history table with past rounds
 * - Chat interface for communication
 * 
 * The layout is responsive and adapts to window height.
 */
export const TraderPageDemo: React.FC = () => {
  return (
    <div className="w-full h-screen">
      <TraderPage />
    </div>
  );
};

export default TraderPageDemo;