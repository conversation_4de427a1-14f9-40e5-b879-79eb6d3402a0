import React, { useRef } from 'react';
import { useSnapshot } from 'valtio';
import { getDomainStore } from '@/api-client';
import type { OrderType, DeTraderHistoryRowElement } from '@/api-client';
import { AuctionChat } from '@/components/chat/AuctionChat';
import { DeTraderHistoryTable, type DeTraderHistoryTableRef } from '@/data-grids/de-trader-history-table/DeTraderHistoryTable';
import { CommonStatusWidget } from '@/widgets/common-status/CommonStatusWidget';
import { OrderEntry } from '@/components/order-entry/OrderEntry';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';

export interface TraderPageProps {
  /** Custom className */
  className?: string;
  /** Custom style */
  style?: React.CSSProperties;
}

/**
 * TraderPage - Main trading interface for traders
 * 
 * Layout:
 * - Top row: Auction name/title
 * - Middle row: Trader status (left) + Order entry (right)
 * - Bottom row: Trader history table (left) + Chat (right)
 * 
 * Features:
 * - Full height layout that adapts to window size
 * - Responsive design with proper spacing
 * - Real-time data from domain store
 * - Interactive components for trading
 */
export const TraderPage: React.FC<TraderPageProps> = ({
  className = '',
  style
}) => {
  const store = useSnapshot(getDomainStore());
  const historyTableRef = useRef<DeTraderHistoryTableRef>(null);

  // Get data from store
  const auctionData = store.de_auction;
  const settings = auctionData?.settings;
  const historyRows = auctionData?.trader_history_rows || [];
  const messages = auctionData?.messages || [];
  const auctionName = auctionData?.settings?.auction_name || 'Auction';

  // Handle order submission
  const handleSubmitOrder = async (orderType: OrderType, quantity: string) => {
    console.log('Order submitted:', { orderType, quantity });
    // TODO: Implement actual order submission via domain store
    // Example: await getDomainStore().submitOrder({ orderType, quantity });
  };

  // Handle message submission
  const handleSubmitMessage = (message: string) => {
    console.log('Message submitted:', message);
    // TODO: Implement actual message submission via domain store
    // Example: await getDomainStore().submitMessage(message);
  };

  // Handle history row selection
  const handleRowSelect = (row: DeTraderHistoryRowElement) => {
    console.log('History row selected:', row);
    // Could scroll to specific round or show details
  };

  return (
    <div 
      className={cn('h-screen bg-gray-50 p-4 grid grid-rows-[auto_minmax(0,_1.2fr)_minmax(0,_2.8fr)] gap-4', className)}
      style={style}
    >
      {/* Top Row - Auction Name */}
      <Card className="w-full">
        <CardContent className="flex items-center justify-center py-4">
          <h1 className="text-2xl font-bold text-gray-800">
            {auctionName}
          </h1>
        </CardContent>
      </Card>

      {/* Middle Row - Trader Status + Order Entry */}
      <div className="grid grid-cols-2 gap-4 min-h-0"> {/* Ensure this row can shrink if needed */}
        {/* Left Half - Trader Status */}
        <Card className="h-full flex flex-col">
          <CardHeader className="pb-3 flex-shrink-0"> {/* Added flex-shrink-0 */}
            <CardTitle className="text-lg font-semibold">Trader Status</CardTitle>
          </CardHeader>
          <CardContent className="flex items-center justify-center flex-1"> {/* Added flex-1 */}
            <CommonStatusWidget 
              showDetails={true}
              timerDiameter={60}
              className="w-full"
            />
          </CardContent>
        </Card>

        {/* Right Half - Order Entry */}
        <OrderEntry 
          onSubmitOrder={handleSubmitOrder}
          className="h-full" // Ensure OrderEntry takes full height of its grid cell
        />
      </div>

      {/* Bottom Row - History Table + Chat */}
      <div className="grid grid-cols-[1fr_320px] gap-4 min-h-0">
        {/* Left Side - Trader History Table */}
        <Card className="h-full flex flex-col min-h-0"> {/* Added flex flex-col */}
          <CardHeader className="pb-3 flex-shrink-0">
            <CardTitle className="text-lg font-semibold">Trading History</CardTitle>
          </CardHeader>
          <CardContent className="p-0 flex-1 min-h-0">
            {settings ? (
              <DeTraderHistoryTable
                ref={historyTableRef}
                height={400} // TODO: Adjust height as needed or make it dynamic
                historyRows={historyRows}
                settings={settings}
                onRowSelect={handleRowSelect}
                className="rounded-none h-full"
              />
            ) : (
              <div className="flex items-center justify-center h-full text-gray-500">
                <div className="text-center">
                  <div className="text-sm">Trading History</div>
                  <div className="text-xs mt-1">No data available</div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Right Side - Chat */}
        <Card className="h-full flex flex-col min-h-0"> {/* Added flex flex-col */}
          <CardHeader className="pb-3 flex-shrink-0">
            <CardTitle className="text-lg font-semibold">Chat</CardTitle>
          </CardHeader>
          <CardContent className="p-0 flex-1 min-h-0">
            <AuctionChat
              is_auctioneer={false}
              messages={messages}
              outer_height={400} // TODO: Adjust height as needed or make it dynamic
              width={296} // TODO: Adjust width as needed or make it dynamic
              onSubmitMessage={handleSubmitMessage}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default TraderPage;