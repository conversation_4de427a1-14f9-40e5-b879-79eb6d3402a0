import React, { useEffect, useRef, useState } from 'react';
import { motion } from 'framer-motion';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Send, User, Megaphone, Settings, AlertCircle, MessageSquare, Radio } from 'lucide-react';
import { cn } from '@/lib/utils';
import { MessageElement, AuMessageType } from '@/api-client';

export interface AuctionChatProps {
  is_auctioneer: boolean;
  messages: readonly MessageElement[];
  outer_height: number;
  width: number;
  onSubmitMessage?: (message: string) => void;
}

const getMessageTypeIcon = (messageType: AuMessageType) => {
  switch (messageType) {
    case AuMessageType.AUCTIONEER_BROADCAST:
      return <Radio className="w-4 h-4" />;
    case AuMessageType.AUCTIONEER_TO_TRADER:
      return <Megaphone className="w-4 h-4" />;
    case AuMessageType.TRADER_TO_AUCTIONEER:
      return <User className="w-4 h-4" />;
    case AuMessageType.SYSTEM_BROADCAST:
      return <Settings className="w-4 h-4" />;
    case AuMessageType.SYSTEM_TO_TRADER:
      return <AlertCircle className="w-4 h-4" />;
    case AuMessageType.SYSTEM_TO_AUCTIONEER:
      return <MessageSquare className="w-4 h-4" />;
    default:
      return <MessageSquare className="w-4 h-4" />;
  }
};

const getMessageTypeColor = (messageType: AuMessageType) => {
  switch (messageType) {
    case AuMessageType.AUCTIONEER_BROADCAST:
      return 'text-blue-600 bg-blue-50 border-blue-200';
    case AuMessageType.AUCTIONEER_TO_TRADER:
      return 'text-green-600 bg-green-50 border-green-200';
    case AuMessageType.TRADER_TO_AUCTIONEER:
      return 'text-purple-600 bg-purple-50 border-purple-200';
    case AuMessageType.SYSTEM_BROADCAST:
      return 'text-orange-600 bg-orange-50 border-orange-200';
    case AuMessageType.SYSTEM_TO_TRADER:
      return 'text-red-600 bg-red-50 border-red-200';
    case AuMessageType.SYSTEM_TO_AUCTIONEER:
      return 'text-gray-600 bg-gray-50 border-gray-200';
    default:
      return 'text-gray-600 bg-gray-50 border-gray-200';
  }
};

const isOutgoingMessage = (message: MessageElement, isAuctioneer: boolean) => {
  if (isAuctioneer) {
    return message.message_type === AuMessageType.AUCTIONEER_BROADCAST ||
           message.message_type === AuMessageType.AUCTIONEER_TO_TRADER;
  } else {
    return message.message_type === AuMessageType.TRADER_TO_AUCTIONEER;
  }
};

export const AuctionChat: React.FC<AuctionChatProps> = ({
  is_auctioneer,
  messages,
  outer_height,
  width,
  onSubmitMessage
}) => {
  const [inputMessage, setInputMessage] = useState('');
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  
  const isPointerDownRef = useRef(false);
  const isNearBottomRef = useRef(true); 

  const messageHeight = is_auctioneer ? outer_height - 58 : outer_height - 87;

  const scrollToBottom = (behavior: ScrollBehavior = 'auto') => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTo({
        top: messagesContainerRef.current.scrollHeight,
        behavior: behavior
      });
    }
  };

  useEffect(() => {
    const container = messagesContainerRef.current;

    const handlePointerDown = () => {
      isPointerDownRef.current = true;
    };

    const handlePointerUp = () => {
      if (isPointerDownRef.current) {
        isPointerDownRef.current = false;
        if (isNearBottomRef.current) {
          scrollToBottom('smooth'); 
        }
      }
    };

    if (container) {
      container.addEventListener('pointerdown', handlePointerDown);
      window.addEventListener('pointerup', handlePointerUp);
    }

    return () => {
      if (container) {
        container.removeEventListener('pointerdown', handlePointerDown);
      }
      window.removeEventListener('pointerup', handlePointerUp);
    };
  }, []); 

  useEffect(() => {
    if (!isPointerDownRef.current) {
      scrollToBottom();
    }
  }, [messages]); 

  const handleScroll = () => {
    if (!messagesContainerRef.current) return;

    const container = messagesContainerRef.current;
    const threshold = 10; 
    const currentIsNearBottom = 
      container.scrollHeight - container.scrollTop - container.clientHeight <= threshold;
    
    isNearBottomRef.current = currentIsNearBottom;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (inputMessage.trim() && onSubmitMessage) {
      onSubmitMessage(inputMessage.trim());
      setInputMessage('');
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.ctrlKey && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  return (
    <div 
      className="flex flex-col bg-background border rounded-lg"
      style={{ width: `${width}px`, height: `${outer_height}px` }}
    >
      <div
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto p-3 space-y-2"
        style={{ height: `${messageHeight}px` }}
        onScroll={handleScroll}
      >
        {messages.length === 0 ? (
          <div className="flex items-center justify-center h-full text-muted-foreground">
            <div className="text-center">
              <MessageSquare className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p>No Messages</p>
            </div>
          </div>
        ) : (
          messages.map((message) => {
            const isOutgoing = isOutgoingMessage(message, is_auctioneer);
            const isTraderMessage = is_auctioneer && message.message_type === AuMessageType.TRADER_TO_AUCTIONEER;
            const colorClasses = getMessageTypeColor(message.message_type);
            
            return (
              <motion.div
                key={message.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.2 }}
                layout 
                className={cn(
                  "flex",
                  isOutgoing ? "justify-end" : "justify-start"
                )}
              >
                <div className={cn(
                  "max-w-[85%] rounded-lg border p-2 shadow-sm transition-all duration-200 hover:shadow-md",
                  colorClasses,
                  isTraderMessage && "ring-2 ring-purple-200 ring-opacity-50"
                )}>
                  <div className="flex items-center gap-1 mb-1">
                    {getMessageTypeIcon(message.message_type)}
                    <span className="text-xs font-medium">
                      {message.from} → {message.to}
                    </span>
                    <span className="text-xs opacity-60 ml-auto">
                      {message.timestamp_label}
                    </span>
                  </div>
                  <div className="text-xs whitespace-pre-wrap break-words">
                    {message.message}
                  </div>
                </div>
              </motion.div>
            );
          })
        )}
      </div>

      {!is_auctioneer && (
        <div className="px-4 py-2 bg-muted/50 text-xs text-muted-foreground border-t">
          Type message to auctioneer<br />
          (message won't be seen by other traders)
        </div>
      )}

      <form onSubmit={handleSubmit} className="p-2 border-t">
        <div className="flex gap-2">
          <Textarea
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Type your message..."
            className="resize-none min-h-[40px] max-h-[120px]"
            style={{ width: `${width - 78}px` }} // Adjusted width slightly for padding/button
          />
          <Button 
            type="submit" 
            size="icon"
            disabled={!inputMessage.trim()}
            className="shrink-0"
          >
            <Send className="w-4 h-4" />
          </Button>
        </div>
      </form>
    </div>
  );
};