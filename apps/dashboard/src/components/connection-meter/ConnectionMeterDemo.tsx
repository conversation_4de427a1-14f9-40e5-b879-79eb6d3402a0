import React, { useState } from 'react';
import { ConnectionMeter } from './ConnectionMeter';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

/**
 * ConnectionMeterDemo - Interactive demo of the ConnectionMeter component
 *
 * This component demonstrates the ConnectionMeter with:
 * - Interactive playground with latency input
 * - Static showcase of different connection qualities
 * - Real-time visual feedback
 */
export const ConnectionMeterDemo: React.FC = () => {
  const [lastPingLatency, setLastPingLatency] = useState(1);

  // Test values representing different connection qualities
  const testValues = [
    { value: 0.9, label: '0.9ms - Excellent (4 bars)' },
    { value: 4.9, label: '4.9ms - Good (3 bars)' },
    { value: 9.9, label: '9.9ms - Fair (2 bars)' },
    { value: 19, label: '19ms - Poor (1 bar)' },
  ];

  const handleLatencyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value);
    if (!isNaN(value) && value >= 0) {
      setLastPingLatency(value);
    }
  };

  const getQualityDescription = (latency: number) => {
    if (latency < 1) return 'Excellent (4 bars)';
    if (latency < 5) return 'Good (3 bars)';
    if (latency < 10) return 'Fair (2 bars)';
    return 'Poor (1 bar)';
  };

  return (
    <div className="space-y-6 p-6">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold">Connection Meter Demo</h1>
        <p className="text-muted-foreground">
          Visual indicator of network connection quality based on ping latency
        </p>
      </div>

      {/* Interactive Playground */}
      <Card>
        <CardHeader>
          <CardTitle>Interactive Playground</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-4">
            <ConnectionMeter lastPingLatency={lastPingLatency} />
            <div className="flex-1 space-y-2">
              <Label htmlFor="latency-input">Ping Latency (ms)</Label>
              <Input
                id="latency-input"
                type="number"
                min="0"
                step="0.1"
                value={lastPingLatency}
                onChange={handleLatencyChange}
                className="w-32"
              />
            </div>
          </div>
          <div className="text-sm text-muted-foreground">
            Current quality: <span className="font-medium">{getQualityDescription(lastPingLatency)}</span>
          </div>
        </CardContent>
      </Card>

      {/* All Test Cases */}
      <Card>
        <CardHeader>
          <CardTitle>Connection Quality Examples</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {testValues.map(({ value, label }) => (
              <div key={value} className="flex items-center gap-4 p-3 rounded-lg border">
                <ConnectionMeter lastPingLatency={value} />
                <div className="flex-1">
                  <div className="font-medium">{label}</div>
                  <div className="text-sm text-muted-foreground">
                    Latency: {value}ms
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Technical Details */}
      <Card>
        <CardHeader>
          <CardTitle>Technical Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <div><strong>Thresholds:</strong></div>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li>&lt; 1ms: 4 bars (Excellent) - All green</li>
              <li>&lt; 5ms: 3 bars (Good) - Orange + Green</li>
              <li>&lt; 10ms: 2 bars (Fair) - Orange + Green</li>
              <li>≥ 10ms: 1 bar (Poor) - Red only</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ConnectionMeterDemo;