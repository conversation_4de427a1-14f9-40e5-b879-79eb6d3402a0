import type { Meta, StoryObj } from '@storybook/react';
import { ConnectionMeter } from './ConnectionMeter';

const meta: Meta<typeof ConnectionMeter> = {
  title: 'Components/ConnectionMeter',
  component: ConnectionMeter,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
ConnectionMeter displays network connection quality based on ping latency.

**Quality Levels:**
- **Excellent (4 bars)**: < 1ms latency - All green bars
- **Good (3 bars)**: < 5ms latency - Orange + green bars  
- **Fair (2 bars)**: < 10ms latency - Orange + green bars
- **Poor (1 bar)**: ≥ 10ms latency - Red bar only

The component uses an SVG with 4 rectangular bars of increasing height to represent signal strength.
        `,
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    lastPingLatency: {
      control: {
        type: 'number',
        min: 0,
        max: 100,
        step: 0.1,
      },
      description: 'Ping latency in milliseconds',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Excellent connection (4 bars)
export const Excellent: Story = {
  args: {
    lastPingLatency: 0.5,
  },
  parameters: {
    docs: {
      description: {
        story: 'Excellent connection quality with latency under 1ms. Shows all 4 green bars.',
      },
    },
  },
};

// Good connection (3 bars)
export const Good: Story = {
  args: {
    lastPingLatency: 3.2,
  },
  parameters: {
    docs: {
      description: {
        story: 'Good connection quality with latency between 1-5ms. Shows 3 bars with orange and green.',
      },
    },
  },
};

// Fair connection (2 bars)
export const Fair: Story = {
  args: {
    lastPingLatency: 7.8,
  },
  parameters: {
    docs: {
      description: {
        story: 'Fair connection quality with latency between 5-10ms. Shows 2 bars with orange and green.',
      },
    },
  },
};

// Poor connection (1 bar)
export const Poor: Story = {
  args: {
    lastPingLatency: 25.0,
  },
  parameters: {
    docs: {
      description: {
        story: 'Poor connection quality with latency over 10ms. Shows only 1 red bar.',
      },
    },
  },
};

// Edge case: Very low latency
export const VeryLowLatency: Story = {
  args: {
    lastPingLatency: 0.1,
  },
  parameters: {
    docs: {
      description: {
        story: 'Extremely low latency scenario. Shows excellent quality with all 4 green bars.',
      },
    },
  },
};

// Edge case: High latency
export const HighLatency: Story = {
  args: {
    lastPingLatency: 100,
  },
  parameters: {
    docs: {
      description: {
        story: 'Very high latency scenario. Shows poor quality with only 1 red bar.',
      },
    },
  },
};

// Threshold boundaries
export const ThresholdBoundaries: Story = {
  render: () => (
    <div className="space-y-4">
      <div className="text-sm font-medium mb-2">Threshold Boundaries:</div>
      <div className="grid grid-cols-2 gap-4">
        <div className="flex items-center gap-2">
          <ConnectionMeter lastPingLatency={0.99} />
          <span className="text-xs">0.99ms (4 bars)</span>
        </div>
        <div className="flex items-center gap-2">
          <ConnectionMeter lastPingLatency={1.0} />
          <span className="text-xs">1.0ms (3 bars)</span>
        </div>
        <div className="flex items-center gap-2">
          <ConnectionMeter lastPingLatency={4.99} />
          <span className="text-xs">4.99ms (3 bars)</span>
        </div>
        <div className="flex items-center gap-2">
          <ConnectionMeter lastPingLatency={5.0} />
          <span className="text-xs">5.0ms (2 bars)</span>
        </div>
        <div className="flex items-center gap-2">
          <ConnectionMeter lastPingLatency={9.99} />
          <span className="text-xs">9.99ms (2 bars)</span>
        </div>
        <div className="flex items-center gap-2">
          <ConnectionMeter lastPingLatency={10.0} />
          <span className="text-xs">10.0ms (1 bar)</span>
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Shows the exact threshold boundaries where bar count changes.',
      },
    },
  },
};

// With custom styling
export const WithCustomStyling: Story = {
  args: {
    lastPingLatency: 2.5,
    className: 'border border-gray-300 rounded p-2 bg-gray-50',
  },
  parameters: {
    docs: {
      description: {
        story: 'Example with custom CSS classes applied via className prop.',
      },
    },
  },
};