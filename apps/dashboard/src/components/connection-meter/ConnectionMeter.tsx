import React, { useMemo } from 'react';
import { cn } from '@/lib/utils';

interface ConnectionMeterProps {
  lastPingLatency: number;
  className?: string;
}

/**
 * ConnectionMeter - Visual indicator of network connection quality
 * 
 * Displays 1-4 bars based on ping latency:
 * - < 1ms: 4 bars (excellent) - all green
 * - < 5ms: 3 bars (good) - orange + green
 * - < 10ms: 2 bars (fair) - orange + green
 * - >= 10ms: 1 bar (poor) - red only
 */
export const ConnectionMeter: React.FC<ConnectionMeterProps> = ({
  lastPingLatency,
  className
}) => {
  const barCount = useMemo(() => {
    if (lastPingLatency < 1) return 4;
    if (lastPingLatency < 5) return 3;
    if (lastPingLatency < 10) return 2;
    return 1;
  }, [lastPingLatency]);

  const getBarStyle = useMemo(() => {
    const fillsForBarCounts = [
      ['red', 'orange', 'green', 'green'],     // 1 bar active
      ['grey', 'orange', 'green', 'green'],   // 2 bars active
      ['grey', 'grey', 'green', 'green'],     // 3 bars active
      ['grey', 'grey', 'grey', 'green'],      // 4 bars active
    ];

    return (barNumber: number) => ({
      stroke: '#333',
      strokeWidth: '1',
      opacity: '1.0',
      fill: fillsForBarCounts[barNumber - 1][barCount - 1],
    });
  }, [barCount]);

  return (
    <svg 
      className={cn('connection-meter', className)} 
      width="40" 
      height="26"
      role="img"
      aria-label={`Connection quality: ${barCount} out of 4 bars (${lastPingLatency}ms latency)`}
    >
      <rect
        x="2"
        y="14"
        rx="1"
        ry="1"
        width="6"
        height="12"
        style={getBarStyle(1)}
      />
      <rect
        x="10"
        y="11"
        rx="1"
        ry="1"
        width="6"
        height="15"
        style={getBarStyle(2)}
      />
      <rect
        x="18"
        y="8"
        rx="1"
        ry="1"
        width="6"
        height="18"
        style={getBarStyle(3)}
      />
      <rect
        x="26"
        y="5"
        rx="1"
        ry="1"
        width="6"
        height="21"
        style={getBarStyle(4)}
      />
    </svg>
  );
};

export default ConnectionMeter;