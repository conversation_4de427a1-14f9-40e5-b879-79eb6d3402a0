import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { OrderType } from '@/api-client';
import { cn } from '@/lib/utils';

export interface OrderEntryProps {
  /** Custom className */
  className?: string;
  /** Custom style */
  style?: React.CSSProperties;
  /** Callback when order is submitted */
  onSubmitOrder?: (orderType: OrderType, quantity: string) => void;
  /** Whether the component is disabled */
  disabled?: boolean;
}

/**
 * OrderEntry - Component for submitting buy/sell orders
 * 
 * Features:
 * - Buy/Sell toggle buttons
 * - Quantity input with validation
 * - Submit button with proper state management
 * - Visual feedback for order type selection
 */
export const OrderEntry: React.FC<OrderEntryProps> = ({
  className = '',
  style,
  onSubmitOrder,
  disabled = false
}) => {
  const [orderType, setOrderType] = useState<OrderType | null>(null);
  const [quantity, setQuantity] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!orderType || !quantity || orderType === OrderType.NONE) {
      return;
    }

    const quantityNum = parseFloat(quantity);
    if (isNaN(quantityNum) || quantityNum <= 0) {
      return;
    }

    setIsSubmitting(true);
    try {
      await onSubmitOrder?.(orderType, quantity);
      // Reset form after successful submission
      setQuantity('');
      setOrderType(null);
    } catch (error) {
      console.error('Order submission failed:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Allow only numbers and decimal point
    if (value === '' || /^\d*\.?\d*$/.test(value)) {
      setQuantity(value);
    }
  };

  const isFormValid = orderType && orderType !== OrderType.NONE && quantity && parseFloat(quantity) > 0;

  return (
    <Card className={cn('w-full', className)} style={style}>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-semibold">Order Entry</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Order Type Selection */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Order Type</Label>
            <div className="flex gap-2">
              <Button
                type="button"
                variant={orderType === OrderType.BUY ? "default" : "outline"}
                className={cn(
                  "flex-1 transition-all",
                  orderType === OrderType.BUY && "bg-green-600 hover:bg-green-700 text-white"
                )}
                onClick={() => setOrderType(OrderType.BUY)}
                disabled={disabled}
              >
                Buy
              </Button>
              <Button
                type="button"
                variant={orderType === OrderType.SELL ? "default" : "outline"}
                className={cn(
                  "flex-1 transition-all",
                  orderType === OrderType.SELL && "bg-red-600 hover:bg-red-700 text-white"
                )}
                onClick={() => setOrderType(OrderType.SELL)}
                disabled={disabled}
              >
                Sell
              </Button>
            </div>
          </div>

          {/* Quantity Input */}
          <div className="space-y-2">
            <Label htmlFor="quantity" className="text-sm font-medium">
              Quantity
            </Label>
            <Input
              id="quantity"
              type="text"
              value={quantity}
              onChange={handleQuantityChange}
              placeholder="Enter quantity"
              disabled={disabled}
              className="font-mono"
            />
          </div>

          {/* Submit Button */}
          <Button
            type="submit"
            className={cn(
              "w-full transition-all",
              orderType === OrderType.BUY && "bg-green-600 hover:bg-green-700",
              orderType === OrderType.SELL && "bg-red-600 hover:bg-red-700"
            )}
            disabled={disabled || !isFormValid || isSubmitting}
          >
            {isSubmitting ? (
              "Submitting..."
            ) : (
              `Submit ${orderType === OrderType.BUY ? 'Buy' : orderType === OrderType.SELL ? 'Sell' : ''} Order`
            )}
          </Button>

          {/* Order Summary */}
          {orderType && orderType !== OrderType.NONE && quantity && (
            <div className="mt-3 p-3 bg-gray-50 rounded-md border">
              <div className="text-sm text-gray-600">
                <strong>Order Summary:</strong>
              </div>
              <div className="text-sm mt-1">
                <span className={cn(
                  "font-medium",
                  orderType === OrderType.BUY ? "text-green-600" : "text-red-600"
                )}>
                  {orderType}
                </span>
                {" "}
                <span className="font-mono">{quantity}</span> units
              </div>
            </div>
          )}
        </form>
      </CardContent>
    </Card>
  );
};

export default OrderEntry;