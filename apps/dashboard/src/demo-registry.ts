import React from 'react';
import { RibbonPlotlyDemo } from './components/RibbonPlotlyDemo';
import { CommonStatusDemo } from './components/CommonStatusDemo';
import { DeRoundTableDemo } from './components/DeRoundTableDemo';
import AuctionListDemoPage from './data-grids/auction-list/AuctionListDemo';
// import { AuctioneerPage } from './components/AuctioneerPage';
import { UserManagementPage } from './components/UserManagementPage';
import { AuctionChatDemo } from './components/chat/AuctionChatDemo';
import { ConnectionMeterDemo } from './components/connection-meter/ConnectionMeterDemo';
import TraderPageDemo from './pages/trader-page/TraderPageDemo';
import { DeAwardTableDemo } from './data-grids/de-award-table/DeAwardTableDemo';
import { DeAuctioneerToolbarDemo } from './data-grids/de-auctioneer-toolbar/DeAuctioneerToolbarDemo';
import { DeAuctioneerToolbarFlowControllerDemo } from './data-grids/de-auctioneer-toolbar-flow-controller/DeAuctioneerToolbarFlowControllerDemo';
//import My3DLineChart from './widgets/3d-ribbon/3d-ribbon-rbf';
import ThreeLineChart3Dv1 from './widgets/3d-ribbon/ThreeLineChart3Dv1';
import ThreeLineChart3Dv2 from './widgets/3d-ribbon/ThreeLineChart3Dv2';
import StepChartO1Mini from './widgets/step-chart/StepChartO1Mini';

/**
 * Demo Registry - Central place to manage all demo pages
 *
 * To add a new demo:
 * 1. Import your demo component
 * 2. Add it to the demoPages array (new demos go first)
 * 3. The navigation will automatically update
 */

export type DemoPage = {
  id: string;
  label: string;
  component: React.ComponentType;
  description?: string;
};

function createDemoPage(
  component: React.ComponentType,
  id: string,
  label?: string,
  description?: string
): DemoPage {
  return {
    id,
    label: label || id,
    component,
    description: description || label || id
  };
}



export const demoPages: DemoPage[] = [
  createDemoPage(DeAuctioneerToolbarFlowControllerDemo, 'DeAuctioneerToolbarFlowControllerDemo', 'DE Auctioneer Toolbar Flow Controller', 'Comprehensive flow controller for auction management with starting price controls and auction flow buttons (Set, Announce, Start, Close, Reset, Next, Award). State-based button management.'),
  createDemoPage(DeAuctioneerToolbarDemo, 'DeAuctioneerToolbarDemo', 'DE Auctioneer Toolbar', 'Comprehensive auctioneer toolbar with flow controls, trader management, and modal windows'),
  createDemoPage(DeAwardTableDemo, 'DeAwardTableDemo', 'DE Award Table', 'Interactive award table showing trader counterparty relationships with filtering'),
  createDemoPage(ConnectionMeterDemo, 'ConnectionMeterDemo', 'Connection Meter', 'Network connection quality indicator with interactive latency testing'),
  createDemoPage(TraderPageDemo, 'TraderPageDemo', 'Trader Page', 'Complete trader interface with status, order entry, history, and chat'),
  createDemoPage(AuctionListDemoPage, 'AuctionListDemo', 'Auction List', 'A list of auctions with interactive elements'),
  createDemoPage(AuctionChatDemo, 'AuctionChatDemo', 'Auction Chat', 'Interactive chat component for auction communication'),
  createDemoPage(StepChartO1Mini, 'StepChartO1Mini'),
  createDemoPage(ThreeLineChart3Dv1, 'ThreeLineChart3Dv1'),
  createDemoPage(ThreeLineChart3Dv2, 'ThreeLineChart3Dv2'),
  // {
  //   id: '3d-ribbon-rbf',
  //   label: '3D Ribbon RBF',
  //   component: My3DLineChart,
  //   description: 'Interactive 3D line chart using @react-three/fiber and drei'
  // },  
  createDemoPage(RibbonPlotlyDemo, 'RibbonPlotlyDemo'),
  createDemoPage(CommonStatusDemo, 'CommonStatusDemo'),
  {
    id: 'round-table',
    label: 'Round Table',
    component: DeRoundTableDemo,
    description: 'Interactive round table with real-time updates and scrolling'
  },
  // {
  //   id: 'auctioneer',
  //   label: 'Auctioneer',
  //   component: AuctioneerPage,
  //   description: 'Auctioneer dashboard with various demo components'
  // },
  {
    id: 'users',
    label: 'Users',
    component: UserManagementPage,
    description: 'User and company management interface'
  },
];

/**
 * Get demo page by ID
 */
export const getDemoPage = (id: string): DemoPage | undefined => {
  return demoPages.find(page => page.id === id);
};

/**
 * Get the default demo page (first in the list)
 */
export const getDefaultDemoPage = (): DemoPage => {
  return demoPages[0];
};
