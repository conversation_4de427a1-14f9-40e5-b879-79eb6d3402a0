import React, { useState } from 'react';
import { DeAuctioneerToolbar } from './DeAuctioneerToolbar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { createTestLiveClientStore } from '@/api-client/helpers/builder/LiveClientStore.factory';
import type { DeFlowControlType } from '@/api-client';

// ========================= DEMO COMPONENT =========================

export const DeAuctioneerToolbarDemo: React.FC = () => {
  // ========================= STATE =========================
  const [showOnlySeen, setShowOnlySeen] = useState(false);
  const [lastAction, setLastAction] = useState<string>('');
  const [actionHistory, setActionHistory] = useState<string[]>([]);

  // Create mock data
  const mockStore = createTestLiveClientStore({
    companyCount: 5,
    userCount: 10,
    deAuctionOverrides: {
      auction_id: 'demo-auction-001',
      settings: {
        auction_name: 'Demo Auction',
        price_label: 'Price ($/MWh)',
        quantity_label: 'Quantity (MW)',
        starting_price: '50.00'
      }
    }
  });

  const mockAuction = mockStore.de_auction;

  // ========================= HANDLERS =========================

  const addToHistory = (action: string) => {
    setLastAction(action);
    setActionHistory(prev => [action, ...prev.slice(0, 9)]); // Keep last 10 actions
  };

  const handleControl = (control: DeFlowControlType) => {
    addToHistory(`Flow Control: ${control}`);
    console.log('Flow control action:', control);
  };

  const handleShowAward = () => {
    addToHistory('Opened Award Modal');
    console.log('Show award modal');
  };

  const handleShowRules = () => {
    addToHistory('Opened Rules Modal');
    console.log('Show rules modal');
  };

  const handleShowTransfers = () => {
    addToHistory('Opened Transfers Modal');
    console.log('Show transfers modal');
  };

  const handleShowSettings = () => {
    addToHistory('Opened Settings Modal');
    console.log('Show settings modal');
  };

  const handleShowOnlySeenChange = (value: boolean) => {
    setShowOnlySeen(value);
    addToHistory(`Show only seen: ${value ? 'enabled' : 'disabled'}`);
  };

  const clearHistory = () => {
    setActionHistory([]);
    setLastAction('');
  };

  // ========================= RENDER =========================

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold">DE Auctioneer Toolbar Demo</h1>
        <p className="text-muted-foreground">
          Interactive demonstration of the auctioneer toolbar component with flow controls, 
          trader management, and modal windows.
        </p>
      </div>

      {/* Auction Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            Auction Information
            <Badge variant="outline">{mockAuction?.auction_id}</Badge>
          </CardTitle>
          <CardDescription>
            Current auction details and status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="font-medium">Name:</span>
              <div className="text-muted-foreground">
                {mockAuction?.settings?.auction_name || 'Demo Auction'}
              </div>
            </div>
            <div>
              <span className="font-medium">Price Label:</span>
              <div className="text-muted-foreground">
                {mockAuction?.settings?.price_label || 'Price ($/MWh)'}
              </div>
            </div>
            <div>
              <span className="font-medium">Quantity Label:</span>
              <div className="text-muted-foreground">
                {mockAuction?.settings?.quantity_label || 'Quantity (MW)'}
              </div>
            </div>
            <div>
              <span className="font-medium">Starting Price:</span>
              <div className="text-muted-foreground">
                ${mockAuction?.settings?.starting_price || '50.00'}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Toolbar Component */}
      <Card>
        <CardHeader>
          <CardTitle>Auctioneer Toolbar</CardTitle>
          <CardDescription>
            Use the toolbar controls to manage the auction flow, traders, and access various windows.
            All actions are logged in the activity panel below.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DeAuctioneerToolbar
            auction={mockAuction}
            showOnlySeen={showOnlySeen}
            onShowOnlySeenChange={handleShowOnlySeenChange}
            onControl={handleControl}
            onShowAward={handleShowAward}
            onShowRules={handleShowRules}
            onShowTransfers={handleShowTransfers}
            onShowSettings={handleShowSettings}
            store={mockStore}
          />
        </CardContent>
      </Card>

      {/* Activity Panel */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Last Action */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Last Action
              <Badge variant={lastAction ? 'default' : 'secondary'}>
                {lastAction ? 'Active' : 'None'}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-sm">
              {lastAction ? (
                <div className="p-3 bg-muted rounded-md font-mono">
                  {lastAction}
                </div>
              ) : (
                <div className="text-muted-foreground italic">
                  No actions performed yet. Try clicking a button in the toolbar above.
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Action History */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Action History
              <div className="flex gap-2">
                <Badge variant="outline">{actionHistory.length}/10</Badge>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearHistory}
                  disabled={actionHistory.length === 0}
                >
                  Clear
                </Button>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 max-h-48 overflow-y-auto">
              {actionHistory.length > 0 ? (
                actionHistory.map((action, index) => (
                  <div
                    key={index}
                    className="text-sm p-2 bg-muted rounded-md font-mono flex items-center justify-between"
                  >
                    <span>{action}</span>
                    <Badge variant="secondary" className="text-xs">
                      #{actionHistory.length - index}
                    </Badge>
                  </div>
                ))
              ) : (
                <div className="text-muted-foreground italic text-sm">
                  Action history will appear here as you interact with the toolbar.
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Feature Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Feature Overview</CardTitle>
          <CardDescription>
            Key features and capabilities of the auctioneer toolbar
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium">Flow Controls</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Set starting price</li>
                <li>• Announce price</li>
                <li>• Start auction</li>
                <li>• Close/reopen rounds</li>
                <li>• Award auction</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">Trader Management</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Add traders to auction</li>
                <li>• Remove traders</li>
                <li>• Filter active/all traders</li>
                <li>• Manage trader assignments</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">Windows & Modals</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Credit history</li>
                <li>• Auction rules</li>
                <li>• Settings management</li>
                <li>• Activity monitoring</li>
                <li>• Match results</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Usage Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Usage Instructions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 text-sm">
            <p>
              <strong>Flow Controls:</strong> Use the starting price and flow control buttons to manage 
              the auction lifecycle. The "Set Starting Price" button opens a modal for price input.
            </p>
            <p>
              <strong>Trader Management:</strong> Add or remove traders using the trader buttons. 
              Use the radio buttons to filter between all traders and active traders only.
            </p>
            <p>
              <strong>Windows:</strong> Access various auction management windows including credit history, 
              rules, settings, and activity monitoring through the windows section.
            </p>
            <p>
              <strong>Modals:</strong> All modal interactions are logged and can be tracked in the 
              activity panel. Each modal provides specific functionality for auction management.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DeAuctioneerToolbarDemo;