import type { Meta, StoryObj } from '@storybook/react';
import { action } from '@storybook/addon-actions';
import { DeAuctioneerToolbar } from './DeAuctioneerToolbar';
import { createTest__DeAuctionValue } from '@/api-client/helpers/demo/DeAuctionValue.helper';
import { createTestLiveClientStore } from '@/api-client/helpers/builder/LiveClientStore.factory';
import type { DeFlowControlType } from '@/api-client';

// ========================= META =========================

const meta: Meta<typeof DeAuctioneerToolbar> = {
  title: 'Data Grids/DE Auctioneer Toolbar',
  component: DeAuctioneerToolbar,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: `
# DE Auctioneer Toolbar

A comprehensive toolbar component for auction management, providing flow controls, trader management, and access to various auction windows.

## Features

- **Flow Controls**: Manage auction lifecycle (start, close rounds, set prices)
- **Trader Management**: Add/remove traders and filter views
- **Modal Windows**: Access to rules, settings, credit history, and more
- **Real-time Actions**: All interactions are logged and tracked

## Usage

The toolbar is designed to be used by auctioneers to control all aspects of an auction session.
        `
      }
    }
  },
  argTypes: {
    auction: {
      description: 'The auction data object',
      control: false
    },
    showOnlySeen: {
      description: 'Whether to show only active traders',
      control: 'boolean'
    },
    onControl: {
      description: 'Callback for flow control actions',
      action: 'onControl'
    },
    onShowAward: {
      description: 'Callback to show award modal',
      action: 'onShowAward'
    },
    onShowRules: {
      description: 'Callback to show rules modal',
      action: 'onShowRules'
    },
    onShowTransfers: {
      description: 'Callback to show transfers modal',
      action: 'onShowTransfers'
    },
    onShowSettings: {
      description: 'Callback to show settings modal',
      action: 'onShowSettings'
    }
  }
};

export default meta;
type Story = StoryObj<typeof DeAuctioneerToolbar>;

// ========================= HELPER FUNCTIONS =========================

const createMockStore = (overrides = {}) => {
  return createTestLiveClientStore({
    companyCount: 5,
    userCount: 10,
    deAuctionOverrides: {
      auction_id: 'story-auction-001',
      settings: {
        auction_name: 'Story Auction',
        price_label: 'Price ($/MWh)',
        quantity_label: 'Quantity (MW)',
        starting_price: '45.00'
      },
      ...overrides
    }
  });
};

const createActions = () => ({
  onControl: action('onControl'),
  onShowAward: action('onShowAward'),
  onShowRules: action('onShowRules'),
  onShowTransfers: action('onShowTransfers'),
  onShowSettings: action('onShowSettings'),
  onShowOnlySeenChange: action('onShowOnlySeenChange')
});

// ========================= STORIES =========================

/**
 * Default toolbar state with a standard auction configuration.
 */
export const Default: Story = {
  args: {
    auction: createMockStore().de_auction,
    showOnlySeen: false,
    ...createActions()
  }
};

/**
 * Toolbar with active trader filter enabled.
 */
export const ActiveTradersOnly: Story = {
  args: {
    auction: createMockStore().de_auction,
    showOnlySeen: true,
    ...createActions()
  }
};

/**
 * Toolbar for a high-value energy auction.
 */
export const EnergyAuction: Story = {
  args: {
    auction: createMockStore({
      auction_id: 'energy-auction-2024',
      settings: {
        auction_name: 'Energy Market Auction 2024',
        price_label: 'Price ($/MWh)',
        quantity_label: 'Capacity (MW)',
        starting_price: '75.50'
      }
    }).de_auction,
    showOnlySeen: false,
    ...createActions()
  }
};

/**
 * Toolbar for a carbon credits auction.
 */
export const CarbonCreditsAuction: Story = {
  args: {
    auction: createMockStore({
      auction_id: 'carbon-credits-001',
      settings: {
        auction_name: 'Carbon Credits Auction Q1',
        price_label: 'Price ($/tCO2)',
        quantity_label: 'Credits (tCO2)',
        starting_price: '25.00'
      }
    }).de_auction,
    showOnlySeen: false,
    ...createActions()
  }
};

/**
 * Toolbar with no auction data (null state).
 */
export const NoAuction: Story = {
  args: {
    auction: null,
    showOnlySeen: false,
    ...createActions()
  }
};

/**
 * Interactive story demonstrating all toolbar features.
 */
export const Interactive: Story = {
  args: {
    auction: createMockStore().de_auction,
    showOnlySeen: false,
    ...createActions()
  },
  parameters: {
    docs: {
      description: {
        story: `
Interactive demonstration of the auctioneer toolbar. Try clicking different buttons to see the actions logged in the Storybook actions panel.

**Available Actions:**
- Flow controls (Set Price, Start Auction, etc.)
- Trader management (Add/Remove)
- Window access (Rules, Settings, etc.)
- Filter controls (All/Active traders)
        `
      }
    }
  }
};

/**
 * Compact layout demonstration.
 */
export const CompactLayout: Story = {
  args: {
    auction: createMockStore().de_auction,
    showOnlySeen: false,
    ...createActions()
  },
  parameters: {
    viewport: {
      defaultViewport: 'tablet'
    },
    docs: {
      description: {
        story: 'Toolbar behavior on smaller screens and tablet devices.'
      }
    }
  }
};

/**
 * Flow control demonstration with different auction states.
 */
export const FlowControlDemo: Story = {
  render: () => {
    const mockStore = createMockStore();
    const actions = createActions();
    
    return (
      <div className="space-y-4 p-4">
        <h3 className="text-lg font-semibold">Flow Control States</h3>
        
        <div className="space-y-4">
          <div>
            <h4 className="text-md font-medium mb-2">Pre-Auction State</h4>
            <DeAuctioneerToolbar
              auction={mockStore.de_auction}
              showOnlySeen={false}
              {...actions}
            />
          </div>
          
          <div>
            <h4 className="text-md font-medium mb-2">Active Auction State</h4>
            <DeAuctioneerToolbar
              auction={mockStore.de_auction}
              showOnlySeen={true}
              {...actions}
            />
          </div>
        </div>
      </div>
    );
  },
  parameters: {
    docs: {
      description: {
        story: 'Demonstration of toolbar behavior in different auction states.'
      }
    }
  }
};

/**
 * Modal interactions demonstration.
 */
export const ModalInteractions: Story = {
  args: {
    auction: createMockStore().de_auction,
    showOnlySeen: false,
    ...createActions()
  },
  parameters: {
    docs: {
      description: {
        story: `
Demonstration of modal interactions. Click the following buttons to see modal behavior:

- **Set Starting Price**: Opens price setting modal
- **Add/Remove Traders**: Opens trader assignment modal
- **Rules**: Opens auction rules modal
- **Settings**: Opens auction settings modal
- **Credit History**: Opens transfers modal
        `
      }
    }
  }
};

/**
 * Responsive behavior demonstration.
 */
export const ResponsiveBehavior: Story = {
  render: () => {
    const mockStore = createMockStore();
    const actions = createActions();
    
    return (
      <div className="space-y-6">
        <div>
          <h4 className="text-md font-medium mb-2">Desktop View</h4>
          <div className="w-full">
            <DeAuctioneerToolbar
              auction={mockStore.de_auction}
              showOnlySeen={false}
              {...actions}
            />
          </div>
        </div>
        
        <div>
          <h4 className="text-md font-medium mb-2">Tablet View</h4>
          <div className="w-3/4">
            <DeAuctioneerToolbar
              auction={mockStore.de_auction}
              showOnlySeen={false}
              {...actions}
            />
          </div>
        </div>
        
        <div>
          <h4 className="text-md font-medium mb-2">Mobile View</h4>
          <div className="w-1/2">
            <DeAuctioneerToolbar
              auction={mockStore.de_auction}
              showOnlySeen={false}
              {...actions}
            />
          </div>
        </div>
      </div>
    );
  },
  parameters: {
    docs: {
      description: {
        story: 'Demonstration of how the toolbar adapts to different screen sizes.'
      }
    }
  }
};