import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import {
  DeAuctionValue,
  DeFlowControlType
} from '@/api-client';

// ========================= TYPES =========================

export interface DeAuctioneerToolbarProps {
  /** The auction data */
  auction: DeAuctionValue | null;
  /** Whether to show only active traders */
  showOnlySeen?: boolean;
  /** Callback when show only seen changes */
  onShowOnlySeenChange?: (value: boolean) => void;
  /** Callback for flow control actions */
  onControl?: (control: DeFlowControlType) => void;
  /** Callback to show award modal */
  onShowAward?: () => void;
  /** Callback to show rules modal */
  onShowRules?: () => void;
  /** Callback to show transfers modal */
  onShowTransfers?: () => void;
  /** Callback to show settings modal */
  onShowSettings?: () => void;
  /** Store object for compatibility */
  store?: any;
}

type TraderFilterType = 'ALL' | 'ACTIVE';

// ========================= COMPONENT =========================

export const DeAuctioneerToolbar: React.FC<DeAuctioneerToolbarProps> = ({
  auction,
  showOnlySeen = false,
  onShowOnlySeenChange,
  onControl,
  onShowAward,
  onShowRules,
  onShowTransfers,
  onShowSettings,
  store
}) => {
  // ========================= STATE =========================
  const [traderFilter, setTraderFilter] = useState<TraderFilterType>('ALL');
  const [showTraderAssignModal, setShowTraderAssignModal] = useState(false);
  const [showPriceModal, setShowPriceModal] = useState(false);
  const [showAwardModal, setShowAwardModal] = useState(false);
  const [showRulesModal, setShowRulesModal] = useState(false);
  const [showEligibilityModal, setShowEligibilityModal] = useState(false);
  const [showTransfersModal, setShowTransfersModal] = useState(false);
  const [isAddTraders, setIsAddTraders] = useState(true);

  // ========================= HANDLERS =========================

  const handleControl = (control: DeFlowControlType) => {
    if (control === 'SET_STARTING_PRICE') {
      setShowPriceModal(true);
    } else {
      onControl?.(control);
    }
  };

  const handleAddTraders = (isAdd: boolean) => {
    setIsAddTraders(isAdd);
    setShowTraderAssignModal(true);
  };

  const handleShowAward = () => {
    setShowAwardModal(true);
    onShowAward?.();
  };

  const handleShowRules = () => {
    setShowRulesModal(true);
    onShowRules?.();
  };

  const handleShowTransfers = () => {
    setShowTransfersModal(true);
    onShowTransfers?.();
  };

  // ========================= RENDER =========================

  return (
    <div className="bg-background border rounded-md p-2 flex items-center gap-4 w-full">
      {/* Spacer */}
      <div className="flex-1" />

      {/* Show Traders Filter */}
      <div className="flex items-center gap-2">
        <Label className="text-sm text-muted-foreground">Show traders:</Label>
        <RadioGroup
          value={traderFilter}
          onValueChange={(value) => setTraderFilter(value as TraderFilterType)}
          className="flex flex-row gap-4"
        >
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="ALL" id="all" />
            <Label htmlFor="all" className="text-sm">all</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="ACTIVE" id="active" />
            <Label htmlFor="active" className="text-sm">active</Label>
          </div>
        </RadioGroup>
      </div>

      {/* Spacer */}
      <div className="flex-1" />

      {/* Starting Price Controls */}
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => handleControl(DeFlowControlType.SET_STARTING_PRICE)}
        >
          Set Starting Price
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => handleControl(DeFlowControlType.ANNOUNCE_STARTING_PRICE)}
        >
          Announce Price
        </Button>
      </div>

      {/* Spacer */}
      <div className="flex-1" />

      {/* Flow Controller */}
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => handleControl(DeFlowControlType.START_AUCTION)}
        >
          Start Auction
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => handleControl(DeFlowControlType.CLOSE_ROUND)}
        >
          Close Round
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => handleControl(DeFlowControlType.NEXT_ROUND)}
        >
          Next Round
        </Button>
      </div>

      {/* Spacer */}
      <div className="flex-1" />

      {/* Traders Management */}
      <div className="flex items-center gap-2">
        <Label className="text-sm text-muted-foreground">Traders:</Label>
        <div className="flex gap-1">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleAddTraders(true)}
          >
            Add
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleAddTraders(false)}
          >
            Remove
          </Button>
        </div>
      </div>

      {/* Spacer */}
      <div className="flex-1" />

      {/* Windows */}
      <div className="flex items-center gap-2">
        <Label className="text-sm text-muted-foreground">Windows:</Label>
        <div className="flex gap-1">
          <Button variant="outline" size="sm">
            Matches
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleShowTransfers}
          >
            Credit History
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleShowRules}
          >
            Max Flow
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleShowRules}
          >
            Activity
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleShowRules}
          >
            Rules
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={onShowSettings}
          >
            Settings
          </Button>
        </div>
      </div>

      {/* Modals */}
      
      {/* Trader Assign Modal */}
      <Dialog open={showTraderAssignModal} onOpenChange={setShowTraderAssignModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {isAddTraders ? 'Add Traders' : 'Remove Traders'}
            </DialogTitle>
          </DialogHeader>
          <div className="p-4">
            <p>Trader assignment functionality would go here.</p>
            <p>Mode: {isAddTraders ? 'Adding' : 'Removing'} traders</p>
          </div>
        </DialogContent>
      </Dialog>

      {/* Price Modal */}
      <Dialog open={showPriceModal} onOpenChange={setShowPriceModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Set Starting Price</DialogTitle>
          </DialogHeader>
          <div className="p-4">
            <p>Starting price setting functionality would go here.</p>
          </div>
        </DialogContent>
      </Dialog>

      {/* Award Modal */}
      <Dialog open={showAwardModal} onOpenChange={setShowAwardModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Award Auction</DialogTitle>
          </DialogHeader>
          <div className="p-4">
            <p>Award functionality would go here.</p>
          </div>
        </DialogContent>
      </Dialog>

      {/* Rules Modal */}
      <Dialog open={showRulesModal} onOpenChange={setShowRulesModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Auction Rules</DialogTitle>
          </DialogHeader>
          <div className="p-4">
            <p>Auction rules would be displayed here.</p>
          </div>
        </DialogContent>
      </Dialog>

      {/* Eligibility Modal */}
      <Dialog open={showEligibilityModal} onOpenChange={setShowEligibilityModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Eligibility</DialogTitle>
          </DialogHeader>
          <div className="p-4">
            <p>Eligibility information would go here.</p>
          </div>
        </DialogContent>
      </Dialog>

      {/* Transfers Modal */}
      <Dialog open={showTransfersModal} onOpenChange={setShowTransfersModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Credit History</DialogTitle>
          </DialogHeader>
          <div className="p-4">
            <p>Transfer history would be displayed here.</p>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default DeAuctioneerToolbar;