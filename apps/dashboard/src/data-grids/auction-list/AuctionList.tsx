import React from 'react';
import { AuctionRowElement, AuctionInstruction } from '../../api-client/connector/types/generated';

interface AuctionListItemProps {
  auction: AuctionRowElement;
  onSelect: (auction: AuctionRowElement) => void;
  onInstruct: (auction: AuctionRowElement, instruction: AuctionInstruction) => void;
}

const AuctionListItem: React.FC<AuctionListItemProps> = ({ auction, onSelect, onInstruct }) => {
  // Assuming types from generated.ts will provide these. 
  // For AuctionRowElement, IS_CLOSED is isClosed, IS_HIDDEN is isHidden
  // TIMESTAMP, AUCTION_NAME are auction_name, starting_time_text might be used for date/time display
  const { auction_name, isClosed, isHidden, starting_time_text, auction_id } = auction; // eslint-disable-line @typescript-eslint/no-unused-vars

  // TODO: Access $auStore.is_auctioneer_or_admin equivalent in React (e.g., from context or a global store like Valtio)
  const isAuctioneerOrAdmin = true; // Placeholder

  // The starting_time_text from AuctionRowElement seems to already be formatted.
  // If more complex date formatting is needed, this function can be expanded.
  const displayTime = starting_time_text || 'Date/Time not available';

  return (
    <li 
      onClick={() => onSelect(auction)} 
      style={{ 
        cursor: 'pointer', 
        borderBottom: '1px solid #374151', 
        padding: '8px', 
        listStyleType: 'none', 
        color: '#f9fafb', 
        backgroundColor: 'transparent',
        transition: 'background-color 0.2s ease'
      }}
      onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#1f2937'}
      onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
    >
      <div style={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
        <div style={{ fontWeight: 'bold', color: '#f9fafb' }}>{displayTime}</div>
        {isAuctioneerOrAdmin && (
          <div>
            {isClosed && (
              <>
                {isHidden && <span style={{ color: '#c0c0c0' }}>(hidden from traders)</span>}
                &nbsp;
                <button 
                  style={{ color: '#1890ff', cursor: 'pointer', background: 'none', border: 'none', padding: 0, fontSize: 'inherit' }} 
                  onClick={(e) => { 
                    e.stopPropagation(); 
                    onInstruct(auction, isHidden ? AuctionInstruction.UNHIDE : AuctionInstruction.HIDE); 
                  }}
                >
                  {isHidden ? 'Unhide' : 'Hide'}
                </button>
              </>
            )}
            &nbsp;
            <button 
              style={{ color: '#1890ff', cursor: 'pointer', background: 'none', border: 'none', padding: 0, fontSize: 'inherit' }} 
              onClick={(e) => { 
                e.stopPropagation(); 
                if(window.confirm('Delete this Auction?')) onInstruct(auction, AuctionInstruction.DELETE); 
              }}
            >
              Delete
            </button>
          </div>
        )}
      </div>
      <div style={{ color: '#f9fafb' }}>{auction_name}</div>
    </li>
  );
};

interface AuctionListProps {
  height: number;
  title: string;
  onAuctionRows: AuctionRowElement[];
  onSelectAuction: (auction: AuctionRowElement) => void;
  onInstructAuction: (auction: AuctionRowElement, instruction: AuctionInstruction) => void;
}

const AuctionList: React.FC<AuctionListProps> = ({ height, title, onAuctionRows, onSelectAuction, onInstructAuction }) => {
  // Assuming AuctionRowElement has a numeric timestamp or a string that can be sorted if needed.
  // The Vue component sorted by TIMESTAMP. AuctionRowElement doesn't have TIMESTAMP directly.
  // If sorting is needed, it should be by a property like `id` or `starting_time_text` if it's sortable, or the parent should provide sorted data.
  // For now, let's assume onAuctionRows is already sorted or doesn't need client-side sorting here.
  // const sortedAuctions = [...onAuctionRows].sort((a, b) => (a.id && b.id ? a.id.localeCompare(b.id) : 0) ); // Example sort by id
  const sortedAuctions = onAuctionRows; // Using as-is for now

  return (
    <div style={{ border: '1px solid #374151', borderRadius: '2px', backgroundColor: '#111827' }}>
      <h3 style={{ padding: '10px 16px', margin: 0, borderBottom: '1px solid #374151', color: '#f9fafb', backgroundColor: '#1f2937' }}>{title}</h3>
      <ul style={{ padding: '0px', margin: 0, height: `${height}px`, overflowY: 'auto' }}>
        {sortedAuctions.length > 0 ? (
          sortedAuctions.map(auction => (
            <AuctionListItem 
              key={auction.auction_id} // Use auction_id from AuctionRowElement as key
              auction={auction} 
              onSelect={onSelectAuction}
              onInstruct={onInstructAuction}
            />
          ))
        ) : (
          <li style={{ padding: '16px', textAlign: 'center', listStyleType: 'none', color: '#9ca3af' }}>No auctions to display.</li>
        )}
      </ul>
    </div>
  );
};

export default AuctionList;

// Basic styles, can be moved to a .css or .less file
// .AuctionListItem:hover {
//   background-color: #d7dff3;
// }