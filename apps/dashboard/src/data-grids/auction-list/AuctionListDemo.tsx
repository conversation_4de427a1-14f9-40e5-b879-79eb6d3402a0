import React from 'react';
import AuctionList from './AuctionList';
import { AuctionRowElement } from '@/api-client/connector/types/generated';

// Mock data for the demo, previously in demo-registry.ts
const mockAuctionsForDemo: AuctionRowElement[] = [
  {
    auction_id: 'demo-auction-1',
    auction_name: 'Live Demo Auction 1',
    isClosed: false,
    isHidden: false,
    starting_time_text: 'Starting: 10:00 AM Thu Oct 26',
    common_state_text: 'Setup',
    auction_design: 'DE',
    id: 'demo-auction-1',
  },
  {
    auction_id: 'demo-auction-2',
    auction_name: 'Live Demo Auction 2',
    isClosed: true,
    isHidden: true,
    starting_time_text: 'Started: 02:30 PM Fri Oct 27',
    common_state_text: 'Closed',
    auction_design: 'DE',
    id: 'demo-auction-2',
  },
  {
    auction_id: 'demo-auction-3',
    auction_name: 'Future Auction',
    isClosed: false,
    isHidden: false,
    starting_time_text: 'Starting: 09:00 AM Mon Nov 01',
    common_state_text: 'Setup',
    auction_design: 'BH',
    id: 'demo-auction-3',
  }
];

const AuctionListDemoPage: React.FC = () => (
  <AuctionList
    height={400}
    title="Auction List Demo"
    onAuctionRows={mockAuctionsForDemo}
    onSelectAuction={(auction) => console.log('Demo: Selected', auction.auction_name)}
    onInstructAuction={(auction, instruction) => console.log('Demo: Instruct', auction.auction_name, instruction)}
  />
);

export default AuctionListDemoPage;