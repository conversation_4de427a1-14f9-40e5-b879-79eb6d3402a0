import React from 'react';
import { StoryFn, Meta } from '@storybook/react'; // Corrected import for StoryFn
import AuctionList from './AuctionList';
import type { AuctionRowElement } from '../../api-client/connector/types/generated';

export default {
  title: 'Data Grids/AuctionList',
  component: AuctionList,
  argTypes: {
    height: { control: 'number' },
    title: { control: 'text' },
    onAuctionRows: { control: 'object' },
    onSelectAuction: { action: 'selected' },
    onInstructAuction: { action: 'instructed' }, // Updated prop name
  },
} as Meta;

const mockAuctions: AuctionRowElement[] = [
  {
    auction_id: 'story-auction-1',
    auction_name: 'Morning Auction - Electronics',
    isClosed: false,
    isHidden: false,
    starting_time_text: 'Starting: 10:00 AM Thu Oct 26',
    common_state_text: 'Setup',
    auction_design: 'DE',
    id: 'story-auction-1',
  },
  {
    auction_id: 'story-auction-2',
    auction_name: 'Afternoon Auction - Furniture',
    isClosed: true,
    isHidden: false,
    starting_time_text: 'Started: 02:30 PM Thu Oct 26',
    common_state_text: 'Closed',
    auction_design: 'DE',
    id: 'story-auction-2',
  },
  {
    auction_id: 'story-auction-3',
    auction_name: 'Next Day Auction - Vehicles',
    isClosed: false,
    isHidden: true,
    starting_time_text: 'Starting: 09:00 AM Fri Oct 27',
    common_state_text: 'Setup',
    auction_design: 'BH',
    id: 'story-auction-3',
  },
  {
    auction_id: 'story-auction-4',
    auction_name: 'Weekend Special - Antiques',
    isClosed: true,
    isHidden: false,
    starting_time_text: 'Started: 11:00 AM Sat Oct 28',
    common_state_text: 'Closed',
    auction_design: 'TE',
    id: 'story-auction-4',
  },
];

const Template: StoryFn<React.ComponentProps<typeof AuctionList>> = (args) => <AuctionList {...args} />;

export const Default = Template.bind({});
Default.args = {
  height: 400,
  title: 'Upcoming Auctions',
  onAuctionRows: mockAuctions,
  onSelectAuction: (auction) => console.log('Selected:', auction.auction_name),
  onInstructAuction: (auction, instruction) => console.log('Instruct:', auction.auction_name, instruction),
};

export const EmptyList = Template.bind({});
EmptyList.args = {
  height: 200,
  title: 'No Auctions Available',
  onAuctionRows: [],
  onSelectAuction: (auction) => console.log('Selected:', auction.auction_name),
  onInstructAuction: (auction, instruction) => console.log('Instruct:', auction.auction_name, instruction),
};

export const TallListWithScrolling = Template.bind({});
TallListWithScrolling.args = {
  height: 250, // Small height to induce scrolling with many items
  title: 'Many Auctions (Scrollable)',
  onAuctionRows: [
    ...mockAuctions,
    ...mockAuctions.map((a, i) => ({ ...a, auction_id: `story-copy-1-${i}-${a.auction_id}`, id: `story-copy-1-${i}-${a.id}`, auction_name: `${a.auction_name} (Copy ${i + 1})` })),
    ...mockAuctions.map((a, i) => ({ ...a, auction_id: `story-copy-2-${i}-${a.auction_id}`, id: `story-copy-2-${i}-${a.id}`, auction_name: `${a.auction_name} (Copy ${i + 5})` })),
  ],
  onSelectAuction: (auction) => console.log('Selected:', auction.auction_name),
  onInstructAuction: (auction, instruction) => console.log('Instruct:', auction.auction_name, instruction),
};