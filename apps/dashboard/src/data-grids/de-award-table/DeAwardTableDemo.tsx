import React from 'react';
import { DeAwardTable } from './DeAwardTable';
import type { DeMatrixRoundElement } from '@/api-client';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

// Sample data matching the structure from the Vue demo
const sampleMatrix: DeMatrixRoundElement = {
  id: 'Round.1',
  round_number: 1,
  nodes: [
    {
      id: 'R_1_T_2425995',
      round: 1,
      cid: '2425995',
      shortname: 'c-1',
      buy_match: 0,
      buy_max: 50,
      buy_min: 0,
      buy_quantity: 0,
      sell_match: 32,
      sell_max: 50,
      sell_min: 0,
      sell_quantity: 50,
      cost: 0,
      cost_str: '$0.00',
      side: 'SELL' as any
    },
    {
      id: 'R_1_T_2425997',
      round: 1,
      cid: '2425997',
      shortname: 'c-2',
      buy_match: 24,
      buy_max: 50,
      buy_min: 0,
      buy_quantity: 12,
      sell_match: 12,
      sell_max: 50,
      sell_min: 0,
      sell_quantity: 0,
      cost: 0,
      cost_str: '$0.00',
      side: 'BUY' as any
    },
    {
      id: 'R_1_T_2425999',
      round: 1,
      cid: '2425999',
      shortname: 'c-3',
      buy_match: 20,
      buy_max: 50,
      buy_min: 0,
      buy_quantity: 20,
      sell_match: 0,
      sell_max: 50,
      sell_min: 0,
      sell_quantity: 0,
      cost: 0,
      cost_str: '$0.00',
      side: 'BUY' as any
    },
    {
      id: 'R_1_T_2426001',
      round: 1,
      cid: '2426001',
      shortname: 'c-4',
      buy_match: 0,
      buy_max: 50,
      buy_min: 0,
      buy_quantity: 0,
      sell_match: 0,
      sell_max: 50,
      sell_min: 0,
      sell_quantity: 0,
      cost: 0,
      cost_str: '$0.00',
      side: 'NONE' as any
    }
  ],
  edges: [
    {
      id: 'R_1_B_2425997_S_2425995',
      r: 1,
      buyer_shortname: 'c-2',
      buyer_cid: '2425997',
      seller_shortname: 'c-1',
      seller_cid: '2425995',
      capacity: 0,
      match: 24,
      credit_str: '$0.00',
      credit_quantity_limit: 0,
      buy_quantity_limit: 0,
      selling_quantity_limit: 0,
      value: 0,
      value_str: '$0.00'
    },
    {
      id: 'R_1_B_2425999_S_2425995',
      r: 1,
      buyer_shortname: 'c-3',
      buyer_cid: '2425999',
      seller_shortname: 'c-1',
      seller_cid: '2425995',
      capacity: 3,
      match: 8,
      credit_str: '$3,200,000.00',
      credit_quantity_limit: 3,
      buy_quantity_limit: 0,
      selling_quantity_limit: 0,
      value: 3200000,
      value_str: '$3,200,000.00'
    },
    {
      id: 'R_1_B_2426001_S_2425995',
      r: 1,
      buyer_shortname: 'c-4',
      buyer_cid: '2426001',
      seller_shortname: 'c-1',
      seller_cid: '2425995',
      capacity: 2,
      match: 0,
      credit_str: '$2,200,000.00',
      credit_quantity_limit: 2,
      buy_quantity_limit: 0,
      selling_quantity_limit: 0,
      value: 2200000,
      value_str: '$2,200,000.00'
    },
    {
      id: 'R_1_B_2425995_S_2425997',
      r: 1,
      buyer_shortname: 'c-1',
      buyer_cid: '2425995',
      seller_shortname: 'c-2',
      seller_cid: '2425997',
      capacity: 5,
      match: 0,
      credit_str: '$5,800,000.00',
      credit_quantity_limit: 5,
      buy_quantity_limit: 0,
      selling_quantity_limit: 0,
      value: 5800000,
      value_str: '$5,800,000.00'
    },
    {
      id: 'R_1_B_2425999_S_2425997',
      r: 1,
      buyer_shortname: 'c-3',
      buyer_cid: '2425999',
      seller_shortname: 'c-2',
      seller_cid: '2425997',
      capacity: 9,
      match: 12,
      credit_str: '$9,000,000.00',
      credit_quantity_limit: 9,
      buy_quantity_limit: 0,
      selling_quantity_limit: 0,
      value: 9000000,
      value_str: '$9,000,000.00'
    },
    {
      id: 'R_1_B_2426001_S_2425997',
      r: 1,
      buyer_shortname: 'c-4',
      buyer_cid: '2426001',
      seller_shortname: 'c-2',
      seller_cid: '2425997',
      capacity: 1,
      match: 0,
      credit_str: '$1,200,000.00',
      credit_quantity_limit: 1,
      buy_quantity_limit: 0,
      selling_quantity_limit: 0,
      value: 1200000,
      value_str: '$1,200,000.00'
    },
    {
      id: 'R_1_B_2425995_S_2425999',
      r: 1,
      buyer_shortname: 'c-1',
      buyer_cid: '2425995',
      seller_shortname: 'c-3',
      seller_cid: '2425999',
      capacity: 2,
      match: 0,
      credit_str: '$2,600,000.00',
      credit_quantity_limit: 2,
      buy_quantity_limit: 0,
      selling_quantity_limit: 0,
      value: 2600000,
      value_str: '$2,600,000.00'
    },
    {
      id: 'R_1_B_2425997_S_2425999',
      r: 1,
      buyer_shortname: 'c-2',
      buyer_cid: '2425997',
      seller_shortname: 'c-3',
      seller_cid: '2425999',
      capacity: 2,
      match: 0,
      credit_str: '$2,100,000.00',
      credit_quantity_limit: 2,
      buy_quantity_limit: 0,
      selling_quantity_limit: 0,
      value: 2100000,
      value_str: '$2,100,000.00'
    },
    {
      id: 'R_1_B_2426001_S_2425999',
      r: 1,
      buyer_shortname: 'c-4',
      buyer_cid: '2426001',
      seller_shortname: 'c-3',
      seller_cid: '2425999',
      capacity: 4,
      match: 0,
      credit_str: '$4,300,000.00',
      credit_quantity_limit: 4,
      buy_quantity_limit: 0,
      selling_quantity_limit: 0,
      value: 4300000,
      value_str: '$4,300,000.00'
    },
    {
      id: 'R_1_B_2425995_S_2426001',
      r: 1,
      buyer_shortname: 'c-1',
      buyer_cid: '2425995',
      seller_shortname: 'c-4',
      seller_cid: '2426001',
      capacity: 3,
      match: 0,
      credit_str: '$3,600,000.00',
      credit_quantity_limit: 3,
      buy_quantity_limit: 0,
      selling_quantity_limit: 0,
      value: 3600000,
      value_str: '$3,600,000.00'
    },
    {
      id: 'R_1_B_2425997_S_2426001',
      r: 1,
      buyer_shortname: 'c-2',
      buyer_cid: '2425997',
      seller_shortname: 'c-4',
      seller_cid: '2426001',
      capacity: 9,
      match: 0,
      credit_str: '$9,700,000.00',
      credit_quantity_limit: 9,
      buy_quantity_limit: 0,
      selling_quantity_limit: 0,
      value: 9700000,
      value_str: '$9,700,000.00'
    },
    {
      id: 'R_1_B_2425999_S_2426001',
      r: 1,
      buyer_shortname: 'c-3',
      buyer_cid: '2425999',
      seller_shortname: 'c-4',
      seller_cid: '2426001',
      capacity: 2,
      match: 0,
      credit_str: '$2,500,000.00',
      credit_quantity_limit: 2,
      buy_quantity_limit: 0,
      selling_quantity_limit: 0,
      value: 2500000,
      value_str: '$2,500,000.00'
    }
  ]
};

export const DeAwardTableDemo: React.FC = () => {
  return (
    <div className="p-6 space-y-6">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">DE Award Table Demo</h1>
        <p className="text-muted-foreground">
          Interactive award table showing trader counterparty relationships with filtering capabilities.
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Award Table - Round {sampleMatrix.round_number}</CardTitle>
          <CardDescription>
            Shows matched trades between counterparties with filtering by buyer/seller roles.
            Use the radio buttons to filter the view.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DeAwardTable
            matrix={sampleMatrix}
            height={500}
            width={660}
          />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Compact View</CardTitle>
          <CardDescription>
            Smaller height for dashboard integration.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DeAwardTable
            matrix={sampleMatrix}
            height={300}
            width={660}
          />
        </CardContent>
      </Card>
    </div>
  );
};

export default DeAwardTableDemo;