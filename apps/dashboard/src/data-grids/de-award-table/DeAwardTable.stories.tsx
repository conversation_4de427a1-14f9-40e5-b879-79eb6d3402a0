import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { DeAwardTable } from './DeAwardTable';
import type { DeMatrixRoundElement } from '@/api-client';

// Sample data for stories
const baseSampleMatrix: DeMatrixRoundElement = {
  id: 'Round.1',
  round_number: 1,
  nodes: [
    {
      id: 'R_1_T_2425995',
      round: 1,
      cid: '2425995',
      shortname: 'c-1',
      buy_match: 0,
      buy_max: 50,
      buy_min: 0,
      buy_quantity: 0,
      sell_match: 32,
      sell_max: 50,
      sell_min: 0,
      sell_quantity: 50,
      cost: 0,
      cost_str: '$0.00',
      side: 'SELL' as any
    },
    {
      id: 'R_1_T_2425997',
      round: 1,
      cid: '2425997',
      shortname: 'c-2',
      buy_match: 24,
      buy_max: 50,
      buy_min: 0,
      buy_quantity: 12,
      sell_match: 12,
      sell_max: 50,
      sell_min: 0,
      sell_quantity: 0,
      cost: 0,
      cost_str: '$0.00',
      side: 'BUY' as any
    },
    {
      id: 'R_1_T_2425999',
      round: 1,
      cid: '2425999',
      shortname: 'c-3',
      buy_match: 20,
      buy_max: 50,
      buy_min: 0,
      buy_quantity: 20,
      sell_match: 0,
      sell_max: 50,
      sell_min: 0,
      sell_quantity: 0,
      cost: 0,
      cost_str: '$0.00',
      side: 'BUY' as any
    },
    {
      id: 'R_1_T_2426001',
      round: 1,
      cid: '2426001',
      shortname: 'c-4',
      buy_match: 0,
      buy_max: 50,
      buy_min: 0,
      buy_quantity: 0,
      sell_match: 0,
      sell_max: 50,
      sell_min: 0,
      sell_quantity: 0,
      cost: 0,
      cost_str: '$0.00',
      side: 'NONE' as any
    }
  ],
  edges: [
    {
      id: 'R_1_B_2425997_S_2425995',
      r: 1,
      buyer_shortname: 'c-2',
      buyer_cid: '2425997',
      seller_shortname: 'c-1',
      seller_cid: '2425995',
      capacity: 0,
      match: 24,
      credit_str: '$0.00',
      credit_quantity_limit: 0,
      buy_quantity_limit: 0,
      selling_quantity_limit: 0,
      value: 0,
      value_str: '$0.00'
    },
    {
      id: 'R_1_B_2425999_S_2425995',
      r: 1,
      buyer_shortname: 'c-3',
      buyer_cid: '2425999',
      seller_shortname: 'c-1',
      seller_cid: '2425995',
      capacity: 3,
      match: 8,
      credit_str: '$3,200,000.00',
      credit_quantity_limit: 3,
      buy_quantity_limit: 0,
      selling_quantity_limit: 0,
      value: 3200000,
      value_str: '$3,200,000.00'
    },
    {
      id: 'R_1_B_2425999_S_2425997',
      r: 1,
      buyer_shortname: 'c-3',
      buyer_cid: '2425999',
      seller_shortname: 'c-2',
      seller_cid: '2425997',
      capacity: 9,
      match: 12,
      credit_str: '$9,000,000.00',
      credit_quantity_limit: 9,
      buy_quantity_limit: 0,
      selling_quantity_limit: 0,
      value: 9000000,
      value_str: '$9,000,000.00'
    }
  ]
};

const emptyMatrix: DeMatrixRoundElement = {
  id: 'Round.Empty',
  round_number: 1,
  nodes: [],
  edges: []
};

const largeMatrix: DeMatrixRoundElement = {
  id: 'Round.Large',
  round_number: 2,
  nodes: [
    {
      id: 'R_2_T_1001',
      round: 2,
      cid: '1001',
      shortname: 'Alpha',
      buy_match: 15,
      buy_max: 100,
      buy_min: 0,
      buy_quantity: 25,
      sell_match: 0,
      sell_max: 100,
      sell_min: 0,
      sell_quantity: 0,
      cost: 0,
      cost_str: '$0.00',
      side: 'BUY' as any
    },
    {
      id: 'R_2_T_1002',
      round: 2,
      cid: '1002',
      shortname: 'Beta',
      buy_match: 0,
      buy_max: 100,
      buy_min: 0,
      buy_quantity: 0,
      sell_match: 30,
      sell_max: 100,
      sell_min: 0,
      sell_quantity: 45,
      cost: 0,
      cost_str: '$0.00',
      side: 'SELL' as any
    },
    {
      id: 'R_2_T_1003',
      round: 2,
      cid: '1003',
      shortname: 'Gamma',
      buy_match: 20,
      buy_max: 100,
      buy_min: 0,
      buy_quantity: 35,
      sell_match: 10,
      sell_max: 100,
      sell_min: 0,
      sell_quantity: 15,
      cost: 0,
      cost_str: '$0.00',
      side: 'BUY' as any
    },
    {
      id: 'R_2_T_1004',
      round: 2,
      cid: '1004',
      shortname: 'Delta',
      buy_match: 5,
      buy_max: 100,
      buy_min: 0,
      buy_quantity: 10,
      sell_match: 25,
      sell_max: 100,
      sell_min: 0,
      sell_quantity: 40,
      cost: 0,
      cost_str: '$0.00',
      side: 'SELL' as any
    }
  ],
  edges: [
    {
      id: 'R_2_B_1001_S_1002',
      r: 2,
      buyer_shortname: 'Alpha',
      buyer_cid: '1001',
      seller_shortname: 'Beta',
      seller_cid: '1002',
      capacity: 15,
      match: 15,
      credit_str: '$15,000,000.00',
      credit_quantity_limit: 15,
      buy_quantity_limit: 0,
      selling_quantity_limit: 0,
      value: 15000000,
      value_str: '$15,000,000.00'
    },
    {
      id: 'R_2_B_1003_S_1002',
      r: 2,
      buyer_shortname: 'Gamma',
      buyer_cid: '1003',
      seller_shortname: 'Beta',
      seller_cid: '1002',
      capacity: 10,
      match: 10,
      credit_str: '$10,500,000.00',
      credit_quantity_limit: 10,
      buy_quantity_limit: 0,
      selling_quantity_limit: 0,
      value: 10500000,
      value_str: '$10,500,000.00'
    },
    {
      id: 'R_2_B_1003_S_1004',
      r: 2,
      buyer_shortname: 'Gamma',
      buyer_cid: '1003',
      seller_shortname: 'Delta',
      seller_cid: '1004',
      capacity: 20,
      match: 10,
      credit_str: '$20,000,000.00',
      credit_quantity_limit: 20,
      buy_quantity_limit: 0,
      selling_quantity_limit: 0,
      value: 20000000,
      value_str: '$20,000,000.00'
    },
    {
      id: 'R_2_B_1001_S_1004',
      r: 2,
      buyer_shortname: 'Alpha',
      buyer_cid: '1001',
      seller_shortname: 'Delta',
      seller_cid: '1004',
      capacity: 5,
      match: 0,
      credit_str: '$5,000,000.00',
      credit_quantity_limit: 5,
      buy_quantity_limit: 0,
      selling_quantity_limit: 0,
      value: 5000000,
      value_str: '$5,000,000.00'
    }
  ]
};

const meta: Meta<typeof DeAwardTable> = {
  title: 'Data Grids/DeAwardTable',
  component: DeAwardTable,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: 'Award table component that displays trader counterparty relationships with filtering capabilities. Shows matched trades between buyers and sellers with value and credit limit information.'
      }
    }
  },
  argTypes: {
    matrix: {
      description: 'Matrix data containing nodes (traders) and edges (relationships)',
      control: { type: 'object' }
    },
    height: {
      description: 'Height of the grid in pixels',
      control: { type: 'number', min: 200, max: 800, step: 50 }
    },
    width: {
      description: 'Width of the grid in pixels',
      control: { type: 'number', min: 400, max: 1200, step: 50 }
    },
    className: {
      description: 'Additional CSS classes',
      control: { type: 'text' }
    }
  },
  tags: ['autodocs']
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    matrix: baseSampleMatrix,
    height: 400,
    width: 660
  },
  parameters: {
    docs: {
      description: {
        story: 'Default award table showing trader relationships with all filters available.'
      }
    }
  }
};

export const Compact: Story = {
  args: {
    matrix: baseSampleMatrix,
    height: 300,
    width: 600
  },
  parameters: {
    docs: {
      description: {
        story: 'Compact version suitable for dashboard integration.'
      }
    }
  }
};

export const Large: Story = {
  args: {
    matrix: largeMatrix,
    height: 500,
    width: 800
  },
  parameters: {
    docs: {
      description: {
        story: 'Larger dataset with more traders and relationships.'
      }
    }
  }
};

export const Empty: Story = {
  args: {
    matrix: emptyMatrix,
    height: 400,
    width: 660
  },
  parameters: {
    docs: {
      description: {
        story: 'Empty state when no data is available.'
      }
    }
  }
};

export const Tall: Story = {
  args: {
    matrix: baseSampleMatrix,
    height: 600,
    width: 660
  },
  parameters: {
    docs: {
      description: {
        story: 'Taller version for detailed viewing.'
      }
    }
  }
};

export const Wide: Story = {
  args: {
    matrix: baseSampleMatrix,
    height: 400,
    width: 900
  },
  parameters: {
    docs: {
      description: {
        story: 'Wider version with more space for columns.'
      }
    }
  }
};