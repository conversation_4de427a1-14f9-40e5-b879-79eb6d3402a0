import React, { useState, useMemo } from 'react';
import type { ColDef, ICellRendererParams, CellClassParams, RowClassParams } from 'ag-grid-community';
import { BaseAgGrid } from '../BaseAgGrid';
import type {
  DeMatrixRoundElement,
  OrderType,
  DeMatrixNodeElement,
  DeMatrixEdgeElement
} from '@/api-client';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';

// ========================= TYPES =========================

export interface DeAwardRowModel {
  id: string;
  is_summary_row: boolean;
  trader_name: string;
  side: OrderType;
  side_str: string;
  counterparty_name: string;
  value: string;
  quantity: string;
  credit_limit: string;
}

export interface DeCounterparty {
  name: string;
  value_str: string;
  quantity: string;
  credit_limit: string;
}

export class DeTraderCounterparties {
  readonly id: string;

  constructor(
    public trader_name: string,
    public side: OrderType,
    public total_quantity_str: string,
    public total_cost_str: string | undefined,
    public counterparties: DeCounterparty[],
  ) {
    this.id = `${trader_name}.${counterparties.length > 0 ? counterparties[0].name : 'summary'}`;
  }
}

type FilterType = 'all' | 'sellers' | 'buyers';

export interface DeAwardTableProps {
  matrix: DeMatrixRoundElement;
  height: number;
  width?: number;
  className?: string;
}

// ========================= HELPER FUNCTIONS =========================

const sort_by_shortname = (nodes: DeMatrixNodeElement[]) =>
  nodes.sort((a, b) => (a.shortname || "").localeCompare(b.shortname || ""));

const matched_nodes = (nodes: DeMatrixNodeElement[]) =>
  nodes.filter(n => (n.buy_match || 0) > 0 || (n.sell_match || 0) > 0);

export function matrix_to_trader_counterparties(matrix: DeMatrixRoundElement): DeTraderCounterparties[] {
  if (matrix === null || !matrix.nodes || !matrix.edges) return [];

  const matched_edges: DeMatrixEdgeElement[] =
    matrix.edges.filter(m => (m.match || 0) > 0);

  return sort_by_shortname(matched_nodes(matrix.nodes))
    .map(node => {
      const side: OrderType =
        (node.buy_match || 0) > 0 ? 'BUY' as OrderType
          : (node.sell_match || 0) > 0 ? 'SELL' as OrderType
            : 'NONE' as OrderType;

      return new DeTraderCounterparties(
        node.shortname || "",
        side,
        String(Math.max(node.buy_match || 0, node.sell_match || 0)),
        node.cost_str,
        matched_edges
          .filter(edge => {
            switch (side) {
              case 'BUY':
                return edge.buyer_shortname === node.shortname;
              case 'SELL':
                return edge.seller_shortname === node.shortname;
              default:
                return false;
            }
          })
          .map(e => ({
            name: (
              side === 'BUY' ? e.seller_shortname
              : side === 'SELL' ? e.buyer_shortname
              : ""
            ) || "",
            value_str: e.value_str || "$0.00",
            quantity: String(e.match || 0),
            credit_limit: e.credit_str || "$0.00"
          })),
      );
    })
    .filter(tc => tc.counterparties.length > 0 || parseFloat(tc.total_quantity_str) > 0);
}

export function counterparties_to_rows(trader_counterparties: Array<DeTraderCounterparties>): DeAwardRowModel[] {
  const to_side_str = (side: OrderType): string => {
    switch (side) {
      case 'BUY': return 'bought';
      case 'SELL': return 'sold';
      default: return '';
    }
  };

  const rows: DeAwardRowModel[] = [];
  let rowIdCounter = 0;

  trader_counterparties.forEach((t: DeTraderCounterparties) => {
    if (t.counterparties.length === 1 && parseFloat(t.total_quantity_str) === parseFloat(t.counterparties[0].quantity)) {
      // Single counterparty, display as one row (summary and detail combined)
      const counterparty: DeCounterparty = t.counterparties[0];
      rows.push({
        id: `${t.id}.${++rowIdCounter}`,
        is_summary_row: true,
        trader_name: t.trader_name,
        side: t.side,
        side_str: to_side_str(t.side),
        counterparty_name: counterparty.name,
        value: counterparty.value_str,
        quantity: t.total_quantity_str,
        credit_limit: counterparty.credit_limit
      });
    } else if (t.counterparties.length >= 1 || parseFloat(t.total_quantity_str) > 0) {
      // Summary Row
      rows.push({
        id: `${t.trader_name}.TOTAL.${++rowIdCounter}`,
        is_summary_row: true,
        trader_name: t.trader_name,
        side: t.side,
        side_str: to_side_str(t.side),
        counterparty_name: '',
        value: t.total_cost_str || "",
        quantity: t.total_quantity_str,
        credit_limit: ''
      });

      // Detail Rows for each counterparty
      t.counterparties.forEach((c: DeCounterparty) => {
        rows.push({
          id: `${t.id}.${c.name}.${++rowIdCounter}`,
          is_summary_row: false,
          trader_name: '',
          side: t.side,
          side_str: `${to_side_str(t.side)} ${t.side === 'BUY' ? 'from:' : 'to:'}`,
          counterparty_name: c.name,
          value: c.value_str,
          quantity: c.quantity,
          credit_limit: c.credit_limit
        });
      });
    }
  });
  return rows;
}

const transformData = (matrix: DeMatrixRoundElement, filter: string): DeAwardRowModel[] => {
  const traderCounterparties = matrix_to_trader_counterparties(matrix);
  const filteredTraderCounterparties = traderCounterparties.filter(tc => {
    if (filter === 'all') return true;
    if (filter === 'sellers') return tc.side === 'SELL';
    if (filter === 'buyers') return tc.side === 'BUY';
    return false;
  });
  return counterparties_to_rows(filteredTraderCounterparties);
};

// ========================= CELL RENDERERS =========================

const TraderCell: React.FC<{ value: string }> = ({ value }) => (
  <div className="font-medium text-foreground">{value}</div>
);

const SideCell: React.FC<{ value: string; side: OrderType }> = ({ value, side }) => (
  <div className={cn(
    "font-medium px-2 py-1 rounded text-xs",
    side === 'BUY' 
      ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
      : side === 'SELL'
      ? "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
      : "bg-gray-100 text-gray-800"
  )}>
    {value}
  </div>
);

const CounterpartyCell: React.FC<{ value: string }> = ({ value }) => (
  <div className="text-foreground">{value}</div>
);

const QuantityCell: React.FC<{ value: string }> = ({ value }) => (
  <div className="text-right font-mono text-foreground">{value}</div>
);

const ValueCell: React.FC<{ value: string }> = ({ value }) => (
  <div className="text-right font-mono text-foreground">{value}</div>
);

const CreditLimitCell: React.FC<{ value: string }> = ({ value }) => (
  <div className="text-right font-mono text-muted-foreground">{value}</div>
);

// ========================= MAIN COMPONENT =========================

export const DeAwardTable: React.FC<DeAwardTableProps> = ({
  matrix,
  height,
  width = 660,
  className
}) => {
  const [filter, setFilter] = useState<FilterType>('all');

  const columnDefs: ColDef[] = useMemo(() => [
    {
      headerName: 'Trader',
      field: 'trader_name',
      width: 100,
      suppressMovable: true,
      cellRenderer: (params: ICellRendererParams<DeAwardRowModel>) => {
        if (!params.data) return null;
        return <TraderCell value={params.value || ''} />;
      },
      cellStyle: (params: CellClassParams<DeAwardRowModel>) => 
        params.data?.is_summary_row && params.data.trader_name ? { fontWeight: 'bold' } : {}
    },
    {
      headerName: 'Side',
      field: 'side_str',
      width: 120,
      suppressMovable: true,
      cellRenderer: (params: ICellRendererParams<DeAwardRowModel>) => {
        if (!params.data) return null;
        const isSummarySide = params.data.is_summary_row && !params.data.counterparty_name;
        return (
          <span className={isSummarySide ? 'font-semibold' : ''}>
            <SideCell value={params.value || ''} side={params.data.side} />
          </span>
        );
      }
    },
    {
      headerName: 'Counterparty',
      field: 'counterparty_name',
      width: 160,
      suppressMovable: true,
      cellRenderer: (params: ICellRendererParams<DeAwardRowModel>) => {
        if (!params.data) return null;
        return <CounterpartyCell value={params.value || ''} />;
      }
    },
    {
      headerName: 'Quantity',
      field: 'quantity',
      width: 80,
      type: 'numericColumn',
      suppressMovable: true,
      cellRenderer: (params: ICellRendererParams<DeAwardRowModel>) => {
        if (!params.data) return null;
        return <QuantityCell value={params.value || ''} />;
      },
      cellStyle: (params: CellClassParams<DeAwardRowModel>) => 
        params.data?.is_summary_row && !params.data.counterparty_name ? { fontWeight: 'bold' } : {}
    },
    {
      headerName: 'Value',
      field: 'value',
      width: 120,
      type: 'numericColumn',
      suppressMovable: true,
      cellRenderer: (params: ICellRendererParams<DeAwardRowModel>) => {
        if (!params.data) return null;
        return <ValueCell value={params.value || ''} />;
      },
      cellStyle: (params: CellClassParams<DeAwardRowModel>) => 
        params.data?.is_summary_row && !params.data.counterparty_name ? { fontWeight: 'bold' } : {}
    },
    {
      headerName: 'Credit limit',
      field: 'credit_limit',
      width: 120,
      type: 'numericColumn',
      suppressMovable: true,
      cellRenderer: (params: ICellRendererParams<DeAwardRowModel>) => {
        if (!params.data) return null;
        return <CreditLimitCell value={params.value || ''} />;
      }
    }
  ], []);

  const rowData: DeAwardRowModel[] = useMemo(() => {
    return transformData(matrix, filter);
  }, [matrix, filter]);

  const defaultColDef: ColDef = {
    sortable: true,
    filter: true,
    resizable: true
  };

  const getRowStyle = (params: RowClassParams<DeAwardRowModel>) => {
    if (params.data?.is_summary_row && params.data.trader_name) {
      return { fontWeight: 'bold' };
    }
    return undefined;
  };

  const gridOptions = useMemo(() => ({
    animateRows: false,
    suppressCellFocus: true,
    suppressRowClickSelection: true
  }), []);

  return (
    <div className={cn("de-award-table", className)}>
      <div className="mb-4">
        <RadioGroup
          value={filter}
          onValueChange={(value) => setFilter(value as FilterType)}
          className="flex flex-row gap-4"
        >
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="all" id="all" />
            <Label htmlFor="all" className="cursor-pointer">All</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="sellers" id="sellers" />
            <Label htmlFor="sellers" className="cursor-pointer">Sellers</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="buyers" id="buyers" />
            <Label htmlFor="buyers" className="cursor-pointer">Buyers</Label>
          </div>
        </RadioGroup>
      </div>
      
      <BaseAgGrid
        rowData={rowData}
        columnDefs={columnDefs}
        height={height}
        width={width}
        gridOptions={gridOptions}
        defaultColDef={defaultColDef}
        enableSorting={true}
        enableFiltering={false}
        enablePagination={false}
        enableSelection={false}
        getRowStyle={getRowStyle}
        className="ag-theme-alpine"
      />
    </div>
  );
};

export default DeAwardTable;