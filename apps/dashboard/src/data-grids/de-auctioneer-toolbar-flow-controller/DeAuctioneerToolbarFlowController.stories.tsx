import type { Meta, StoryObj } from '@storybook/react';
import { action } from '@storybook/addon-actions';
import { DeAuctioneerToolbarFlowController } from './DeAuctioneerToolbarFlowController';
import { DeAuctioneerState, DeFlowControlType } from '@/api-client';

// ========================= META =========================

const meta: Meta<typeof DeAuctioneerToolbarFlowController> = {
  title: 'Data Grids/DE Auctioneer Toolbar Flow Controller',
  component: DeAuctioneerToolbarFlowController,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
# DE Auctioneer Toolbar Flow Controller

A comprehensive flow controller component for auction management, combining starting price controls and auction flow controls.

## Features

- **Starting Price Controls**: Set and Announce buttons for managing starting prices
- **Auction Flow Controls**: Start, Close, Reset, Next, and Award buttons
- **State-based Button Management**: Button states controlled by auctioneer state
- **Confirmation Dialogs**: Reset action includes confirmation dialog
- **Event Handling**: Comprehensive event system for all control actions

## Button Functions

- **Set**: Set the starting price for the auction
- **Announce**: Announce the starting price to participants
- **Start**: Start the auction round
- **Close**: Close the current round
- **Reset**: Reset/reopen the current round (with confirmation)
- **Next**: Move to the next round
- **Award**: Show the award management interface

## State Management

The component accepts a \`DeAuctioneerState\` prop that determines which buttons are enabled/disabled based on the current auction state.
        `,
      },
    },
  },
  argTypes: {
    auctioneerState: {
      control: {
        type: 'select',
      },
      options: [
        'STARTING_PRICE_NOT_SET',
        'STARTING_PRICE_SET',
        'STARTING_PRICE_ANNOUNCED',
        'ROUND_OPEN_ALL_ORDERS_NOT_IN',
        'ROUND_OPEN_ALL_ORDERS_IN',
        'ROUND_CLOSED_NOT_AWARDABLE',
        'ROUND_CLOSED_AWARDABLE',
        'AUCTION_CLOSED'
      ],
      description: 'Current auctioneer state that controls button availability',
    },
    onControl: {
      action: 'control-triggered',
      description: 'Callback fired when a flow control button is clicked',
    },
    onShowAward: {
      action: 'show-award',
      description: 'Callback fired when the Award button is clicked',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes for styling',
    },
  },
  args: {
    auctioneerState: 'STARTING_PRICE_NOT_SET' as DeAuctioneerState,
    onControl: action('onControl'),
    onShowAward: action('onShowAward'),
    className: '',
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// ========================= STORIES =========================

/**
 * Default state showing all buttons in their initial configuration.
 */
export const Default: Story = {
  args: {
    auctioneerState: 'STARTING_PRICE_NOT_SET' as DeAuctioneerState,
  },
};

/**
 * State when starting price has been set but not announced.
 */
export const StartingPriceSet: Story = {
  args: {
    auctioneerState: 'STARTING_PRICE_SET' as DeAuctioneerState,
  },
};

/**
 * State when starting price has been announced.
 */
export const StartingPriceAnnounced: Story = {
  args: {
    auctioneerState: 'STARTING_PRICE_ANNOUNCED' as DeAuctioneerState,
  },
};

/**
 * State when round is open but not all orders are in.
 */
export const RoundOpenOrdersNotIn: Story = {
  args: {
    auctioneerState: 'ROUND_OPEN_ALL_ORDERS_NOT_IN' as DeAuctioneerState,
  },
};

/**
 * State when round is open and all orders are in.
 */
export const RoundOpenAllOrdersIn: Story = {
  args: {
    auctioneerState: 'ROUND_OPEN_ALL_ORDERS_IN' as DeAuctioneerState,
  },
};

/**
 * State when round is closed but not awardable.
 */
export const RoundClosedNotAwardable: Story = {
  args: {
    auctioneerState: 'ROUND_CLOSED_NOT_AWARDABLE' as DeAuctioneerState,
  },
};

/**
 * State when round is closed and awardable.
 */
export const RoundClosedAwardable: Story = {
  args: {
    auctioneerState: 'ROUND_CLOSED_AWARDABLE' as DeAuctioneerState,
  },
};

/**
 * State when auction is closed.
 */
export const AuctionClosed: Story = {
  args: {
    auctioneerState: 'AUCTION_CLOSED' as DeAuctioneerState,
  },
};

/**
 * Component with custom styling.
 */
export const WithCustomStyling: Story = {
  args: {
    auctioneerState: 'STARTING_PRICE_NOT_SET' as DeAuctioneerState,
    className: 'p-6 bg-blue-50 border-2 border-blue-200 rounded-lg shadow-lg',
  },
};

/**
 * Interactive playground for testing different states and actions.
 */
export const InteractivePlayground: Story = {
  args: {
    auctioneerState: 'STARTING_PRICE_NOT_SET' as DeAuctioneerState,
  },
  render: (args) => {
    return (
      <div className="space-y-4">
        <div className="p-4 bg-gray-100 rounded">
          <h3 className="font-semibold mb-2">Current State: {args.auctioneerState}</h3>
          <p className="text-sm text-gray-600">
            Use the controls below to test different button interactions.
          </p>
        </div>
        <DeAuctioneerToolbarFlowController auctioneerState={DeAuctioneerState.STARTING_PRICE_NOT_SET} onControl={function (controlType: DeFlowControlType): void {
          throw new Error('Function not implemented.');
        } } onShowAward={function (): void {
          throw new Error('Function not implemented.');
        } } {...args} />
        <div className="p-4 bg-yellow-50 border border-yellow-200 rounded">
          <h4 className="font-medium text-yellow-800">Note:</h4>
          <p className="text-sm text-yellow-700">
            Button states are currently using placeholder logic. 
            Actual state machine logic will be implemented later.
          </p>
        </div>
      </div>
    );
  },
};

/**
 * Compact layout for smaller spaces.
 */
export const CompactLayout: Story = {
  args: {
    auctioneerState: 'ROUND_OPEN_ALL_ORDERS_IN' as DeAuctioneerState,
    className: 'text-xs',
  },
  parameters: {
    layout: 'centered',
  },
};

/**
 * All states showcase - displays multiple instances with different states.
 */
export const AllStatesShowcase: Story = {
  render: () => {
    const states: DeAuctioneerState[] = [
      'STARTING_PRICE_NOT_SET',
      'STARTING_PRICE_SET', 
      'STARTING_PRICE_ANNOUNCED',
      'ROUND_OPEN_ALL_ORDERS_NOT_IN',
      'ROUND_OPEN_ALL_ORDERS_IN',
      'ROUND_CLOSED_NOT_AWARDABLE',
      'ROUND_CLOSED_AWARDABLE',
      'AUCTION_CLOSED'
    ] as DeAuctioneerState[];

    return (
      <div className="space-y-6">
        <h2 className="text-xl font-bold">All Auctioneer States</h2>
        {states.map((state) => (
          <div key={state} className="space-y-2">
            <h3 className="font-semibold text-sm text-gray-700">
              {state.replace(/_/g, ' ')}
            </h3>
            <div className="p-3 border rounded bg-gray-50">
              <DeAuctioneerToolbarFlowController
                auctioneerState={state}
                onControl={action('onControl')}
                onShowAward={action('onShowAward')}
              />
            </div>
          </div>
        ))}
      </div>
    );
  },
  parameters: {
    layout: 'padded',
  },
};