import React, { useState } from 'react';
import { DeAuctioneerToolbarFlowController } from './DeAuctioneerToolbarFlowController';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import type { DeFlowControlType, DeAuctioneerState } from '@/api-client';

export function DeAuctioneerToolbarFlowControllerDemo() {
  const [currentState, setCurrentState] = useState<DeAuctioneerState>('STARTING_PRICE_NOT_SET' as DeAuctioneerState);
  const [lastAction, setLastAction] = useState<string>('');
  const [actionHistory, setActionHistory] = useState<string[]>([]);

  const handleControl = (controlType: DeFlowControlType) => {
    const action = `Control: ${controlType}`;
    setLastAction(action);
    setActionHistory(prev => [action, ...prev.slice(0, 9)]); // Keep last 10 actions
    console.log('Flow Control Action:', controlType);
  };

  const handleShowAward = () => {
    const action = 'Show Award Modal';
    setLastAction(action);
    setActionHistory(prev => [action, ...prev.slice(0, 9)]);
    console.log('Show Award Modal triggered');
  };

  const handleStateChange = (newState: DeAuctioneerState) => {
    setCurrentState(newState);
    const action = `State changed to: ${newState}`;
    setLastAction(action);
    setActionHistory(prev => [action, ...prev.slice(0, 9)]);
  };

  const availableStates: DeAuctioneerState[] = [
    'STARTING_PRICE_NOT_SET',
    'STARTING_PRICE_SET',
    'STARTING_PRICE_ANNOUNCED',
    'ROUND_OPEN_ALL_ORDERS_NOT_IN',
    'ROUND_OPEN_ALL_ORDERS_IN',
    'ROUND_CLOSED_NOT_AWARDABLE',
    'ROUND_CLOSED_AWARDABLE',
    'AUCTION_CLOSED'
  ] as DeAuctioneerState[];

  return (
    <div className="p-6 space-y-6">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold">DE Auctioneer Toolbar Flow Controller Demo</h1>
        <p className="text-gray-600">
          Interactive demo of the auctioneer flow controller with starting price and auction control buttons.
        </p>
      </div>

      {/* State Selector */}
      <Card>
        <CardHeader>
          <CardTitle>Current Auctioneer State</CardTitle>
          <CardDescription>
            Select different states to see how the toolbar responds
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">Current State:</span>
              <Badge variant="outline">{currentState}</Badge>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              {availableStates.map((state) => (
                <button
                  key={state}
                  onClick={() => handleStateChange(state)}
                  className={`p-2 text-xs rounded border transition-colors ${
                    currentState === state
                      ? 'bg-blue-100 border-blue-300 text-blue-800'
                      : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                  }`}
                >
                  {state.replace(/_/g, ' ')}
                </button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Flow Controller Component */}
      <Card>
        <CardHeader>
          <CardTitle>Flow Controller</CardTitle>
          <CardDescription>
            Test the starting price and auction control buttons
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DeAuctioneerToolbarFlowController
            auctioneerState={currentState}
            onControl={handleControl}
            onShowAward={handleShowAward}
            className="p-4 border rounded-lg bg-gray-50"
          />
        </CardContent>
      </Card>

      {/* Action History */}
      <Card>
        <CardHeader>
          <CardTitle>Action History</CardTitle>
          <CardDescription>
            Recent actions and state changes
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {lastAction && (
              <div className="p-2 bg-green-50 border border-green-200 rounded text-sm">
                <strong>Last Action:</strong> {lastAction}
              </div>
            )}
            <div className="space-y-1 max-h-48 overflow-y-auto">
              {actionHistory.map((action, index) => (
                <div
                  key={index}
                  className="p-2 bg-gray-50 border rounded text-xs text-gray-600"
                >
                  {action}
                </div>
              ))}
            </div>
            {actionHistory.length === 0 && (
              <div className="text-sm text-gray-500 italic">
                No actions yet. Click buttons above to see history.
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Instructions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm text-gray-600">
            <p>• Change the auctioneer state using the buttons above</p>
            <p>• Click the flow control buttons to trigger actions</p>
            <p>• The Reset button shows a confirmation dialog</p>
            <p>• All actions are logged in the action history</p>
            <p>• Button states will be controlled by the state machine (placeholder logic for now)</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default DeAuctioneerToolbarFlowControllerDemo;