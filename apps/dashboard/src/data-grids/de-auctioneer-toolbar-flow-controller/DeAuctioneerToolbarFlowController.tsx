import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import type { DeFlowControlType, DeAuctioneerState } from '@/api-client';

export interface DeAuctioneerToolbarFlowControllerProps {
  auctioneerState: DeAuctioneerState;
  onControl: (controlType: DeFlowControlType) => void;
  onShowAward: () => void;
  className?: string;
}

export function DeAuctioneerToolbarFlowController({
  auctioneerState,
  onControl,
  onShowAward,
  className = '',
}: DeAuctioneerToolbarFlowControllerProps) {
  // Placeholder function for determining button states
  // This will be replaced with actual state machine logic
  const isControlDisabled = (controlType: DeFlowControlType): boolean => {
    // TODO: Implement actual state machine logic based on auctioneerState
    // For now, return false to enable all buttons for testing
    return false;
  };

  const handleControlClick = (controlType: DeFlowControlType) => {
    onControl(controlType);
  };

  const handleResetConfirm = () => {
    onControl('REOPEN_ROUND' as DeFlowControlType);
  };

  return (
    <div className={`de-auctioneer-toolbar-flow-controller ${className}`}>
      <div className="inline-flex">
        <Button
          variant="outline"
          size="sm"
          onClick={() => handleControlClick('SET_STARTING_PRICE' as DeFlowControlType)}
          disabled={isControlDisabled('SET_STARTING_PRICE' as DeFlowControlType)}
          data-testid="set-starting-price"
          className="rounded-r-none border-r-0 px-2 py-1 h-auto leading-tight"
        >
          <div className="text-center">
            <div>Set</div>
            <div>Price</div>
          </div>
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => handleControlClick('ANNOUNCE_STARTING_PRICE' as DeFlowControlType)}
          disabled={isControlDisabled('ANNOUNCE_STARTING_PRICE' as DeFlowControlType)}
          data-testid="announce-starting-price"
          className="rounded-none border-r-0 px-2 py-1 h-auto leading-tight"
        >
          <div className="text-center">
            <div>Announce</div>
            <div>Price</div>
          </div>
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => handleControlClick('START_AUCTION' as DeFlowControlType)}
          disabled={isControlDisabled('START_AUCTION' as DeFlowControlType)}
          data-testid="start-auction"
          className="rounded-none border-r-0 px-2 py-1 h-auto leading-tight"
        >
          <div className="text-center">
            <div>Start</div>
            <div>Round</div>
          </div>
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => handleControlClick('CLOSE_ROUND' as DeFlowControlType)}
          disabled={isControlDisabled('CLOSE_ROUND' as DeFlowControlType)}
          data-testid="close-round"
          className="rounded-none border-r-0 px-2 py-1 h-auto leading-tight"
        >
          <div className="text-center">
            <div>Close</div>
            <div>Round</div>
          </div>
        </Button>
        
        {/* Reset button with confirmation dialog */}
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button
               variant="outline"
               size="sm"
               disabled={isControlDisabled('REOPEN_ROUND' as DeFlowControlType)}
               data-testid="reset-round"
               className="rounded-none border-r-0 px-2 py-1 h-auto leading-tight"
             >
               <div className="text-center">
                 <div>Reset</div>
                 <div>Round</div>
               </div>
             </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Reset this Round?</AlertDialogTitle>
              <AlertDialogDescription>
                This action will reopen the current round. Are you sure you want to continue?
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>No</AlertDialogCancel>
              <AlertDialogAction onClick={handleResetConfirm}>Yes</AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        <Button
           variant="outline"
           size="sm"
           onClick={() => handleControlClick('NEXT_ROUND' as DeFlowControlType)}
           disabled={isControlDisabled('NEXT_ROUND' as DeFlowControlType)}
           data-testid="next-round"
           className="rounded-none border-r-0 px-2 py-1 h-auto leading-tight"
         >
           <div className="text-center">
             <div>Next</div>
             <div>Round</div>
           </div>
         </Button>
         <Button
           variant="outline"
           size="sm"
           onClick={onShowAward}
           data-testid="show-award"
           className="rounded-l-none px-2 py-1 h-auto leading-tight"
         >
           <div className="text-center">
             <div>Award</div>
             <div>Auction</div>
           </div>
         </Button>
      </div>
    </div>
  );
}

export default DeAuctioneerToolbarFlowController;