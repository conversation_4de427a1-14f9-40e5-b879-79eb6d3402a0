import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react';
import React, { useState } from 'react';
import { OrderConstraints } from './OrderConstraints';
import { OrderConstraintsWithRange } from './OrderConstraintsWithRange';
import { DeBidConstraints, OrderType } from '../../api-client/connector/types/generated';

const meta: Meta<typeof OrderConstraints> = {
  title: 'Widgets/OrderConstraints',
  component: OrderConstraints,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A volume meter component that visually displays bidding eligibility constraints in auction trading.'
      }
    }
  },
  argTypes: {
    height: {
      control: { type: 'range', min: 20, max: 100, step: 5 }
    },
    width: {
      control: { type: 'range', min: 100, max: 500, step: 10 }
    },
    maxQuantity: {
      control: { type: 'range', min: 10, max: 100, step: 5 }
    },
    tickStep: {
      control: { type: 'range', min: 5, max: 20, step: 5 }
    },
    tickFontSize: {
      control: { type: 'range', min: 8, max: 16, step: 1 }
    },
    orderQuantity: {
      control: { type: 'range', min: 0, max: 50, step: 1 }
    },
    orderType: {
      control: { type: 'select' },
      options: [OrderType.BUY, OrderType.SELL, OrderType.NONE, null]
    },
    priceChange: {
      control: { type: 'select' },
      options: ['up', 'down', null]
    },
    showLabels: {
      control: { type: 'boolean' }
    }
  }
};

export default meta;
type Story = StoryObj<typeof OrderConstraints>;

// Default constraints for stories
const defaultConstraints: DeBidConstraints = {
  max_buy_quantity: 40,
  max_sell_quantity: 20,
  min_buy_quantity: 0,
  min_sell_quantity: 0
};

export const Default: Story = {
  args: {
    constraints: defaultConstraints,
    height: 30,
    width: 300,
    maxQuantity: 50,
    orderQuantity: 10,
    orderType: OrderType.BUY,
    showLabels: true,
    tickFontSize: 12,
    tickStep: 10,
    priceChange: null
  }
};

export const BuyOrder: Story = {
  args: {
    ...Default.args,
    orderType: OrderType.BUY,
    orderQuantity: 25
  }
};

export const SellOrder: Story = {
  args: {
    ...Default.args,
    orderType: OrderType.SELL,
    orderQuantity: 15
  }
};

export const NoOrder: Story = {
  args: {
    ...Default.args,
    orderType: null,
    orderQuantity: null
  }
};

export const PriceIncrease: Story = {
  args: {
    ...Default.args,
    priceChange: 'up' as const
  }
};

export const PriceDecrease: Story = {
  args: {
    ...Default.args,
    priceChange: 'down' as const
  }
};

export const OneSidedBuy: Story = {
  args: {
    ...Default.args,
    constraints: {
      max_buy_quantity: 40,
      min_buy_quantity: 10,
      max_sell_quantity: 0,
      min_sell_quantity: 0
    },
    orderType: OrderType.BUY,
    orderQuantity: 20
  }
};

export const OneSidedSell: Story = {
  args: {
    ...Default.args,
    constraints: {
      max_buy_quantity: 0,
      min_buy_quantity: 0,
      max_sell_quantity: 40,
      min_sell_quantity: 10
    },
    orderType: OrderType.SELL,
    orderQuantity: 20
  }
};

export const WithRange: Story = {
  render: (args) => (
    <OrderConstraintsWithRange
      orderQuantity={args.orderQuantity || 10}
      orderType={args.orderType || OrderType.BUY}
      constraints={args.constraints || defaultConstraints}
      quantityLabel="MMlb"
    />
  ),
  args: {
    constraints: defaultConstraints,
    orderQuantity: 10,
    orderType: OrderType.BUY
  }
};

// Interactive demo story - matching Vue demo functionality
export const InteractiveDemo: Story = {
  render: () => {
    const [width, setWidth] = useState(300);
    const [height, setHeight] = useState(30);
    const [maxQuantity, setMaxQuantity] = useState(50);
    const [tickStep, setTickStep] = useState(10);
    const [tickFontSize, setTickFontSize] = useState(12);
    const [orderType, setOrderType] = useState<OrderType>(OrderType.BUY);
    const [orderQuantity, setOrderQuantity] = useState(10);
    const [showLabels, setShowLabels] = useState(true);
    const [constraints, setConstraints] = useState<DeBidConstraints>({
      max_buy_quantity: 40,
      max_sell_quantity: 20,
      min_buy_quantity: 0,
      min_sell_quantity: 0
    });

    return (
      <div style={{ padding: '20px', display: 'flex', flexDirection: 'column', gap: '20px', height: '600px' }}>
        <OrderConstraints
          constraints={constraints}
          height={height}
          maxQuantity={maxQuantity}
          orderQuantity={orderQuantity}
          orderType={orderType}
          showLabels={showLabels}
          tickFontSize={tickFontSize}
          tickStep={tickStep}
          width={width}
        />

        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: '20px', marginTop: '20px' }}>
          {/* Width Control */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
            <label style={{ textAlign: 'right', width: '120px' }}>Width:</label>
            <input
              type="number"
              value={width}
              onChange={(e) => setWidth(Number(e.target.value))}
              style={{ width: '100px' }}
            />
            <input
              type="range"
              min="100"
              max="500"
              value={width}
              onChange={(e) => setWidth(Number(e.target.value))}
              style={{ width: '100px' }}
            />
          </div>

          {/* Height Control */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
            <label style={{ textAlign: 'right', width: '120px' }}>Height:</label>
            <input
              type="number"
              value={height}
              onChange={(e) => setHeight(Number(e.target.value))}
              style={{ width: '100px' }}
            />
            <input
              type="range"
              min="20"
              max="100"
              value={height}
              onChange={(e) => setHeight(Number(e.target.value))}
              style={{ width: '100px' }}
            />
          </div>

          {/* Max Quantity Control */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
            <label style={{ textAlign: 'right', width: '120px' }}>Max Quantity:</label>
            <input
              type="number"
              value={maxQuantity}
              onChange={(e) => setMaxQuantity(Number(e.target.value))}
              style={{ width: '100px' }}
            />
            <input
              type="range"
              min="10"
              max="100"
              value={maxQuantity}
              onChange={(e) => setMaxQuantity(Number(e.target.value))}
              style={{ width: '100px' }}
            />
          </div>

          {/* Tick Step Control */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
            <label style={{ textAlign: 'right', width: '120px' }}>Tick Step:</label>
            <input
              type="number"
              value={tickStep}
              onChange={(e) => setTickStep(Number(e.target.value))}
              style={{ width: '100px' }}
            />
            <input
              type="range"
              min="5"
              max="20"
              value={tickStep}
              onChange={(e) => setTickStep(Number(e.target.value))}
              style={{ width: '100px' }}
            />
          </div>

          {/* Tick Font Size Control */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
            <label style={{ textAlign: 'right', width: '120px' }}>Tick Font Size:</label>
            <input
              type="number"
              value={tickFontSize}
              onChange={(e) => setTickFontSize(Number(e.target.value))}
              style={{ width: '100px' }}
            />
            <input
              type="range"
              min="8"
              max="16"
              value={tickFontSize}
              onChange={(e) => setTickFontSize(Number(e.target.value))}
              style={{ width: '100px' }}
            />
          </div>

          {/* Order Type Control */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
            <label style={{ textAlign: 'right', width: '120px' }}>Order Side:</label>
            <div style={{ display: 'flex', gap: '5px' }}>
              <button
                onClick={() => setOrderType(OrderType.NONE)}
                style={{
                  width: '50px',
                  padding: '4px',
                  backgroundColor: orderType === OrderType.NONE ? '#ccc' : 'white'
                }}
              >
                None
              </button>
              <button
                onClick={() => setOrderType(OrderType.BUY)}
                style={{
                  width: '50px',
                  padding: '4px',
                  backgroundColor: orderType === OrderType.BUY ? '#ccc' : 'white'
                }}
              >
                Buy
              </button>
              <button
                onClick={() => setOrderType(OrderType.SELL)}
                style={{
                  width: '50px',
                  padding: '4px',
                  backgroundColor: orderType === OrderType.SELL ? '#ccc' : 'white'
                }}
              >
                Sell
              </button>
            </div>
          </div>

          {/* Order Quantity Control */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
            <label style={{ textAlign: 'right', width: '120px' }}>Order Quantity:</label>
            <input
              type="number"
              value={orderQuantity}
              onChange={(e) => setOrderQuantity(Number(e.target.value))}
              style={{ width: '100px' }}
            />
            <input
              type="range"
              min="0"
              max={maxQuantity}
              value={orderQuantity}
              onChange={(e) => setOrderQuantity(Number(e.target.value))}
              style={{ width: '100px' }}
            />
          </div>

          {/* Show Labels Control */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
            <label style={{ textAlign: 'right', width: '120px' }}>Show Labels:</label>
            <input
              type="checkbox"
              checked={showLabels}
              onChange={(e) => setShowLabels(e.target.checked)}
            />
          </div>

          {/* Constraints Controls */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
            <label style={{ textAlign: 'right', width: '120px' }}>Max Buy:</label>
            <input
              type="number"
              value={constraints.max_buy_quantity}
              onChange={(e) => setConstraints({
                ...constraints,
                max_buy_quantity: Number(e.target.value)
              })}
              style={{ width: '100px' }}
            />
          </div>

          <div style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
            <label style={{ textAlign: 'right', width: '120px' }}>Min Buy:</label>
            <input
              type="number"
              value={constraints.min_buy_quantity}
              onChange={(e) => setConstraints({
                ...constraints,
                min_buy_quantity: Number(e.target.value)
              })}
              style={{ width: '100px' }}
            />
          </div>

          <div style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
            <label style={{ textAlign: 'right', width: '120px' }}>Max Sell:</label>
            <input
              type="number"
              value={constraints.max_sell_quantity}
              onChange={(e) => setConstraints({
                ...constraints,
                max_sell_quantity: Number(e.target.value)
              })}
              style={{ width: '100px' }}
            />
          </div>

          <div style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
            <label style={{ textAlign: 'right', width: '120px' }}>Min Sell:</label>
            <input
              type="number"
              value={constraints.min_sell_quantity}
              onChange={(e) => setConstraints({
                ...constraints,
                min_sell_quantity: Number(e.target.value)
              })}
              style={{ width: '100px' }}
            />
          </div>
        </div>
      </div>
    );
  }
};
