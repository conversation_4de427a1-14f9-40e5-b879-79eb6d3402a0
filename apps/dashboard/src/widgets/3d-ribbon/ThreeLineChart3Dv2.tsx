import React, { useEffect, useRef, useState } from 'react';
import * as THREE from 'three';

const ThreeLineChart3Dv2 = () => {
  const mountRef = useRef(null);
  const sceneRef = useRef(null);
  const rendererRef = useRef(null);
  const cameraRef = useRef(null);
  const controlsRef = useRef(null);
  const raycasterRef = useRef(new THREE.Raycaster());
  const mouseRef = useRef(new THREE.Vector2());
  
  const traderObjectsGroupRef = useRef(new THREE.Group());
  const axesGroupRef = useRef(new THREE.Group());
  
  const [hoveredTrader, setHoveredTrader] = useState(null);
  const [xScale, setXScale] = useState(1);
  const [yScale, setYScale] = useState(1);
  const [zScale, setZScale] = useState(1);
  const [auctionData, setAuctionData] = useState([]);
  
  // Orbit control limits
  const [minDistance, setMinDistance] = useState(10);
  const [maxDistance, setMaxDistance] = useState(500);
  const [minPolarAngle, setMinPolarAngle] = useState(0.1);
  const [maxPolarAngle, setMaxPolarAngle] = useState(Math.PI - 0.1);

  // Constants
  const NUM_TRADERS = 5;
  const NUM_ROUNDS = 10;
  const INITIAL_QUANTITY = 100;
  const Z_SPACING_PER_TRADER = 8;
  const MARKER_SIZE = 0.4;
  const TRADER_COLORS = [0xffde3d, 0xffb732, 0xff9234, 0xff6d3a, 0xff4530];

  // Initial camera settings
  const INITIAL_CAMERA_SETTINGS = {
    sphericalRadius: 100,
    sphericalPhi: Math.PI / 3,
    sphericalTheta: Math.PI / 4,
    panX: 0,
    panY: 0,
    panZ: 0
  };

  // Data generation
  const generateAuctionData = (numTraders, numRounds, initialQuantity) => {
    const tradersData = [];
    for (let t = 0; t < numTraders; t++) {
      const trader = { 
        traderId: `Trader ${t + 1}`, 
        bids: [],
        color: TRADER_COLORS[t % TRADER_COLORS.length]
      };
      let currentQuantity = initialQuantity;
      for (let r = 1; r <= numRounds; r++) {
        if (r === 1) {
          trader.bids.push({ round: r, quantity: initialQuantity });
        } else {
          if (currentQuantity > 0) {
            const decrease = Math.floor(Math.random() * 5) + 1;
            currentQuantity = Math.max(0, currentQuantity - decrease);
          }
          trader.bids.push({ round: r, quantity: currentQuantity });
        }
      }
      tradersData.push(trader);
    }
    return tradersData;
  };

  // Create text sprite
  const createTextSprite = (text, size = 1) => {
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    canvas.width = 256;
    canvas.height = 64;
    
    context.font = `${size * 40}px Arial`;
    context.fillStyle = 'white';
    context.textAlign = 'center';
    context.textBaseline = 'middle';
    context.fillText(text, 128, 32);
    
    const texture = new THREE.CanvasTexture(canvas);
    const spriteMaterial = new THREE.SpriteMaterial({ map: texture });
    const sprite = new THREE.Sprite(spriteMaterial);
    sprite.scale.set(size * 5, size * 1.25, 1);
    
    return sprite;
  };

  // Create axes and grid
  const createAxesAndGrid = (scene, xScale, yScale, zScale) => {
    axesGroupRef.current.clear();
    
    const axisColor = 0x999999;
    const gridColor = 0x444444;
    const tickColor = 0xcccccc;
    const labelColor = 0xffffff;
    const axisLabelSize = 2.0;
    const tickLabelSize = 1.5;
    
    const axisLineMaterial = new THREE.LineBasicMaterial({ color: axisColor });
    const gridLineMaterial = new THREE.LineBasicMaterial({ color: gridColor });
    
    const X_MAX = NUM_ROUNDS * xScale;
    const Y_MAX = INITIAL_QUANTITY * yScale;
    const Z_MAX = (NUM_TRADERS - 1) * Z_SPACING_PER_TRADER * zScale;
    
    // Main axes
    const xAxis = new THREE.BufferGeometry().setFromPoints([
      new THREE.Vector3(0, 0, 0), 
      new THREE.Vector3(X_MAX + 1 * xScale, 0, 0)
    ]);
    const yAxis = new THREE.BufferGeometry().setFromPoints([
      new THREE.Vector3(0, 0, 0), 
      new THREE.Vector3(0, Y_MAX + 5 * yScale, 0)
    ]);
    const zAxis = new THREE.BufferGeometry().setFromPoints([
      new THREE.Vector3(0, 0, 0), 
      new THREE.Vector3(0, 0, Z_MAX + Z_SPACING_PER_TRADER * 0.5 * zScale)
    ]);
    
    axesGroupRef.current.add(new THREE.Line(xAxis, axisLineMaterial));
    axesGroupRef.current.add(new THREE.Line(yAxis, axisLineMaterial));
    axesGroupRef.current.add(new THREE.Line(zAxis, axisLineMaterial));
    
    // Axis labels
    const xLabel = createTextSprite('Round', axisLabelSize);
    xLabel.position.set(X_MAX / 2, -axisLabelSize * 2.5, 0);
    axesGroupRef.current.add(xLabel);
    
    const yLabel = createTextSprite('Quantity', axisLabelSize);
    yLabel.position.set(-axisLabelSize * 3, Y_MAX / 2, 0);
    axesGroupRef.current.add(yLabel);
    
    const zLabel = createTextSprite('Trader', axisLabelSize);
    zLabel.position.set(0, -axisLabelSize * 2.5, Z_MAX / 2);
    axesGroupRef.current.add(zLabel);
    
    // Y-axis ticks
    for (let i = 0; i <= INITIAL_QUANTITY; i += 20) {
      if (i === 0) continue;
      const tickLabel = createTextSprite(i.toString(), tickLabelSize * 0.7);
      tickLabel.position.set(-tickLabelSize * 1.5, i * yScale, 0);
      axesGroupRef.current.add(tickLabel);
      
      const tickLine = new THREE.BufferGeometry().setFromPoints([
        new THREE.Vector3(-0.5, i * yScale, 0), 
        new THREE.Vector3(0.5, i * yScale, 0)
      ]);
      axesGroupRef.current.add(new THREE.Line(tickLine, axisLineMaterial));
    }
    
    // X-axis ticks
    for (let i = 0; i <= NUM_ROUNDS; i += 2) {
      if (i === 0) continue;
      const tickLabel = createTextSprite(i.toString(), tickLabelSize * 0.6);
      tickLabel.position.set(i * xScale, -tickLabelSize * 1.2, 0);
      axesGroupRef.current.add(tickLabel);
    }
    
    // Grid lines
    const xGridStep = 1 * xScale;
    const yGridStep = 10 * yScale;
    const zGridStep = Z_SPACING_PER_TRADER * zScale;
    
    // XZ plane (floor)
    for (let i = 0; i <= NUM_ROUNDS; i++) {
      const line = new THREE.BufferGeometry().setFromPoints([
        new THREE.Vector3(i * xScale, 0, 0), 
        new THREE.Vector3(i * xScale, 0, Z_MAX)
      ]);
      axesGroupRef.current.add(new THREE.Line(line, gridLineMaterial));
    }
    
    for (let i = 0; i < NUM_TRADERS; i++) {
      const zPos = i * Z_SPACING_PER_TRADER * zScale;
      const line = new THREE.BufferGeometry().setFromPoints([
        new THREE.Vector3(0, 0, zPos), 
        new THREE.Vector3(X_MAX, 0, zPos)
      ]);
      axesGroupRef.current.add(new THREE.Line(line, gridLineMaterial));
    }
    
    // XY plane (back wall)
    for (let i = 0; i <= NUM_ROUNDS; i++) {
      const line = new THREE.BufferGeometry().setFromPoints([
        new THREE.Vector3(i * xScale, 0, 0), 
        new THREE.Vector3(i * xScale, Y_MAX, 0)
      ]);
      axesGroupRef.current.add(new THREE.Line(line, gridLineMaterial));
    }
    
    for (let i = 0; i <= Y_MAX; i += yGridStep) {
      const line = new THREE.BufferGeometry().setFromPoints([
        new THREE.Vector3(0, i, 0), 
        new THREE.Vector3(X_MAX, i, 0)
      ]);
      axesGroupRef.current.add(new THREE.Line(line, gridLineMaterial));
    }
    
    // YZ plane (side wall)
    for (let i = 0; i < NUM_TRADERS; i++) {
      const zPos = i * Z_SPACING_PER_TRADER * zScale;
      const line = new THREE.BufferGeometry().setFromPoints([
        new THREE.Vector3(0, 0, zPos), 
        new THREE.Vector3(0, Y_MAX, zPos)
      ]);
      axesGroupRef.current.add(new THREE.Line(line, gridLineMaterial));
    }
    
    for (let i = 0; i <= Y_MAX; i += yGridStep) {
      const line = new THREE.BufferGeometry().setFromPoints([
        new THREE.Vector3(0, i, 0), 
        new THREE.Vector3(0, i, Z_MAX)
      ]);
      axesGroupRef.current.add(new THREE.Line(line, gridLineMaterial));
    }
    
    scene.add(axesGroupRef.current);
  };

  // Plot data
  const plotData = (data, xScale, yScale, zScale) => {
    traderObjectsGroupRef.current.clear();
    
    data.forEach((trader, traderIndex) => {
      const points = [];
      const color = trader.color;
      const lineMaterial = new THREE.LineBasicMaterial({ 
        color: color, 
        linewidth: 2,
        transparent: true,
        opacity: 0.8
      });
      const markerMaterial = new THREE.MeshBasicMaterial({ 
        color: color,
        transparent: true,
        opacity: 0.8
      });
      const markerGeometry = new THREE.SphereGeometry(MARKER_SIZE, 8, 8);
      
      // Create group for this trader
      const traderGroup = new THREE.Group();
      traderGroup.userData = {
        traderId: trader.traderId,
        traderIndex: traderIndex,
        color: color,
        bids: trader.bids
      };
      
      trader.bids.forEach(bid => {
        const x = bid.round * xScale;
        const y = bid.quantity * yScale;
        const z = traderIndex * Z_SPACING_PER_TRADER * zScale;
        const pointVec = new THREE.Vector3(x, y, z);
        points.push(pointVec);
        
        const marker = new THREE.Mesh(markerGeometry, markerMaterial.clone());
        marker.position.copy(pointVec);
        marker.userData.traderId = trader.traderId;
        marker.userData.traderIndex = traderIndex;
        traderGroup.add(marker);
      });
      
      if (points.length > 1) {
        const lineGeometry = new THREE.BufferGeometry().setFromPoints(points);
        const line = new THREE.Line(lineGeometry, lineMaterial);
        line.userData.traderId = trader.traderId;
        line.userData.traderIndex = traderIndex;
        traderGroup.add(line);
      }
      
      traderObjectsGroupRef.current.add(traderGroup);
    });
    
    sceneRef.current.add(traderObjectsGroupRef.current);
  };

  // Reset view function
  const resetView = () => {
    if (controlsRef.current) {
      controlsRef.current.spherical.radius = INITIAL_CAMERA_SETTINGS.sphericalRadius;
      controlsRef.current.spherical.phi = INITIAL_CAMERA_SETTINGS.sphericalPhi;
      controlsRef.current.spherical.theta = INITIAL_CAMERA_SETTINGS.sphericalTheta;
      controlsRef.current.panOffset.set(
        INITIAL_CAMERA_SETTINGS.panX,
        INITIAL_CAMERA_SETTINGS.panY,
        INITIAL_CAMERA_SETTINGS.panZ
      );
      controlsRef.current.update();
    }
  };

  // Initialize Three.js
  useEffect(() => {
    if (!mountRef.current) return;

    // Scene setup
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0x111111);
    sceneRef.current = scene;

    // Camera setup
    const aspect = 600 / 600;
    const camera = new THREE.PerspectiveCamera(45, aspect, 1, 2000);
    camera.position.set(NUM_ROUNDS * 1.5, INITIAL_QUANTITY * 0.7, (NUM_TRADERS - 1) * Z_SPACING_PER_TRADER + 40);
    cameraRef.current = camera;

    // Renderer setup
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(600, 600);
    mountRef.current.appendChild(renderer.domElement);
    rendererRef.current = renderer;

    // Lighting
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.9);
    scene.add(ambientLight);
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.3);
    directionalLight.position.set(0.5, 1, 0.75).normalize();
    scene.add(directionalLight);

    // Manual orbit controls
    const controls = {
      enabled: true,
      target: new THREE.Vector3(
        NUM_ROUNDS * xScale / 2, 
        INITIAL_QUANTITY * yScale / 3, 
        (NUM_TRADERS - 1) * Z_SPACING_PER_TRADER * zScale / 2
      ),
      minDistance: minDistance,
      maxDistance: maxDistance,
      spherical: new THREE.Spherical(
        INITIAL_CAMERA_SETTINGS.sphericalRadius,
        INITIAL_CAMERA_SETTINGS.sphericalPhi,
        INITIAL_CAMERA_SETTINGS.sphericalTheta
      ),
      panOffset: new THREE.Vector3(
        INITIAL_CAMERA_SETTINGS.panX,
        INITIAL_CAMERA_SETTINGS.panY,
        INITIAL_CAMERA_SETTINGS.panZ
      ),
      update: function() {
        const offset = new THREE.Vector3();
        const quat = new THREE.Quaternion().setFromUnitVectors(camera.up, new THREE.Vector3(0, 1, 0));
        const quatInverse = quat.clone().invert();
        
        offset.setFromSpherical(this.spherical);
        offset.applyQuaternion(quat);
        camera.position.copy(this.target).add(offset).add(this.panOffset);
        camera.lookAt(this.target.clone().add(this.panOffset));
      }
    };
    controlsRef.current = controls;

    // Mouse controls
    let mouseDown = false;
    let mouseButton = -1;
    let mouseX = 0;
    let mouseY = 0;

    const handleMouseDown = (e) => {
      mouseDown = true;
      mouseButton = e.button;
      mouseX = e.clientX;
      mouseY = e.clientY;
      e.preventDefault();
    };

    const handleMouseMove = (e) => {
      const rect = renderer.domElement.getBoundingClientRect();
      mouseRef.current.x = ((e.clientX - rect.left) / rect.width) * 2 - 1;
      mouseRef.current.y = -((e.clientY - rect.top) / rect.height) * 2 + 1;

      if (!mouseDown) return;

      const deltaX = e.clientX - mouseX;
      const deltaY = e.clientY - mouseY;

      if (mouseButton === 0) { // Left mouse - rotate
        controls.spherical.theta -= deltaX * 0.01;
        controls.spherical.phi = Math.max(minPolarAngle, Math.min(maxPolarAngle, controls.spherical.phi - deltaY * 0.01));
      } else if (mouseButton === 2) { // Right mouse - pan
        const panSpeed = 0.5;
        controls.panOffset.x -= deltaX * panSpeed * 0.1;
        controls.panOffset.y += deltaY * panSpeed * 0.1;
      }

      controls.update();
      mouseX = e.clientX;
      mouseY = e.clientY;
    };

    const handleMouseUp = () => {
      mouseDown = false;
    };

    const handleWheel = (e) => {
      e.preventDefault();
      controls.spherical.radius = Math.max(
        minDistance, 
        Math.min(maxDistance, controls.spherical.radius + e.deltaY * 0.05)
      );
      controls.update();
    };

    const handleContextMenu = (e) => {
      e.preventDefault();
    };

    renderer.domElement.addEventListener('mousedown', handleMouseDown);
    renderer.domElement.addEventListener('mousemove', handleMouseMove);
    renderer.domElement.addEventListener('mouseup', handleMouseUp);
    renderer.domElement.addEventListener('wheel', handleWheel);
    renderer.domElement.addEventListener('contextmenu', handleContextMenu);

    // Generate initial data
    const data = generateAuctionData(NUM_TRADERS, NUM_ROUNDS, INITIAL_QUANTITY);
    setAuctionData(data);
    
    // Create scene elements
    createAxesAndGrid(scene, xScale, yScale, zScale);
    plotData(data, xScale, yScale, zScale);
    controls.update();

    // Animation loop
    let animationFrame;
    const animate = () => {
      animationFrame = requestAnimationFrame(animate);
      
      // Hover detection
      raycasterRef.current.setFromCamera(mouseRef.current, camera);
      const intersects = raycasterRef.current.intersectObjects(traderObjectsGroupRef.current.children, true);
      
      // Reset all trader groups
      traderObjectsGroupRef.current.children.forEach(group => {
        group.children.forEach(child => {
          if (child.material) {
            child.material.opacity = 0.8;
            child.material.needsUpdate = true;
          }
        });
      });
      
      // Highlight hovered trader
      if (intersects.length > 0) {
        const intersectedObject = intersects[0].object;
        const traderIndex = intersectedObject.userData.traderIndex;
        
        if (traderIndex !== undefined) {
          const traderGroup = traderObjectsGroupRef.current.children[traderIndex];
          if (traderGroup) {
            traderGroup.children.forEach(child => {
              if (child.material) {
                child.material.opacity = 1.0;
                child.material.needsUpdate = true;
              }
            });
            
            const traderData = auctionData[traderIndex];
            if (traderData) {
              setHoveredTrader({
                ...traderData,
                index: traderIndex
              });
            }
          }
        }
      } else {
        setHoveredTrader(null);
      }
      
      renderer.render(scene, camera);
    };
    animate();

    // Data update interval
    const updateInterval = setInterval(() => {
      const newData = generateAuctionData(NUM_TRADERS, NUM_ROUNDS, INITIAL_QUANTITY);
      setAuctionData(newData);
      plotData(newData, xScale, yScale, zScale);
    }, 3000);

    // Cleanup
    return () => {
      cancelAnimationFrame(animationFrame);
      clearInterval(updateInterval);
      
      renderer.domElement.removeEventListener('mousedown', handleMouseDown);
      renderer.domElement.removeEventListener('mousemove', handleMouseMove);
      renderer.domElement.removeEventListener('mouseup', handleMouseUp);
      renderer.domElement.removeEventListener('wheel', handleWheel);
      renderer.domElement.removeEventListener('contextmenu', handleContextMenu);
      
      if (mountRef.current && renderer.domElement) {
        mountRef.current.removeChild(renderer.domElement);
      }
      
      renderer.dispose();
    };
  }, [minDistance, maxDistance, minPolarAngle, maxPolarAngle]);

  // Handle scale updates
  useEffect(() => {
    if (sceneRef.current && auctionData.length > 0) {
      createAxesAndGrid(sceneRef.current, xScale, yScale, zScale);
      plotData(auctionData, xScale, yScale, zScale);
      
      // Update camera target based on new scales
      if (controlsRef.current) {
        controlsRef.current.target.set(
          NUM_ROUNDS * xScale / 2,
          INITIAL_QUANTITY * yScale / 3,
          (NUM_TRADERS - 1) * Z_SPACING_PER_TRADER * zScale / 2
        );
        controlsRef.current.update();
      }
    }
  }, [xScale, yScale, zScale, auctionData]);

  // Update orbit limits
  useEffect(() => {
    if (controlsRef.current) {
      controlsRef.current.minDistance = minDistance;
      controlsRef.current.maxDistance = maxDistance;
      controlsRef.current.spherical.radius = Math.max(
        minDistance,
        Math.min(maxDistance, controlsRef.current.spherical.radius)
      );
      controlsRef.current.update();
    }
  }, [minDistance, maxDistance]);

  return (
    <div style={{ 
      display: 'flex',
      gap: '20px',
      backgroundColor: '#111',
      padding: '20px',
      borderRadius: '5px',
      color: '#e0e0e0',
      fontFamily: 'Arial, sans-serif'
    }}>
      <div>
        <h2 style={{ marginBottom: '15px' }}>3D Auction Clock Chart</h2>
        <div 
          ref={mountRef} 
          style={{ 
            border: '1px solid #444',
            borderRadius: '5px',
            width: '600px',
            height: '600px'
          }} 
        />
        
        {/* Scale Controls */}
        <div style={{
          marginTop: '15px',
          padding: '10px',
          backgroundColor: 'rgba(30, 30, 30, 0.85)',
          borderRadius: '5px',
          border: '1px solid #444'
        }}>
          <h4 style={{ margin: '0 0 10px 0', fontSize: '14px' }}>Scale Controls</h4>
          
          <div style={{ marginBottom: '8px' }}>
            <label style={{ display: 'inline-block', width: '80px' }}>X-Scale:</label>
            <input
              type="range"
              min="0.5"
              max="2"
              step="0.1"
              value={xScale}
              onChange={(e) => setXScale(parseFloat(e.target.value))}
              style={{ width: '150px', marginRight: '10px' }}
            />
            <span style={{ width: '40px', display: 'inline-block' }}>{xScale.toFixed(1)}x</span>
          </div>
          
          <div style={{ marginBottom: '8px' }}>
            <label style={{ display: 'inline-block', width: '80px' }}>Y-Scale:</label>
            <input
              type="range"
              min="0.5"
              max="2"
              step="0.1"
              value={yScale}
              onChange={(e) => setYScale(parseFloat(e.target.value))}
              style={{ width: '150px', marginRight: '10px' }}
            />
            <span style={{ width: '40px', display: 'inline-block' }}>{yScale.toFixed(1)}x</span>
          </div>
          
          <div>
            <label style={{ display: 'inline-block', width: '80px' }}>Z-Scale:</label>
            <input
              type="range"
              min="0.5"
              max="2"
              step="0.1"
              value={zScale}
              onChange={(e) => setZScale(parseFloat(e.target.value))}
              style={{ width: '150px', marginRight: '10px' }}
            />
            <span style={{ width: '40px', display: 'inline-block' }}>{zScale.toFixed(1)}x</span>
          </div>
        </div>
        
        {/* Orbit Controls */}
        <div style={{
          marginTop: '10px',
          padding: '10px',
          backgroundColor: 'rgba(30, 30, 30, 0.85)',
          borderRadius: '5px',
          border: '1px solid #444'
        }}>
          <h4 style={{ margin: '0 0 10px 0', fontSize: '14px' }}>Orbit Limits</h4>
          
          <div style={{ marginBottom: '8px' }}>
            <label style={{ display: 'inline-block', width: '100px' }}>Min Distance:</label>
            <input
              type="range"
              min="5"
              max="100"
              step="5"
              value={minDistance}
              onChange={(e) => setMinDistance(parseInt(e.target.value))}
              style={{ width: '130px', marginRight: '10px' }}
            />
            <span style={{ width: '40px', display: 'inline-block' }}>{minDistance}</span>
          </div>
          
          <div style={{ marginBottom: '8px' }}>
            <label style={{ display: 'inline-block', width: '100px' }}>Max Distance:</label>
            <input
              type="range"
              min="100"
              max="1000"
              step="50"
              value={maxDistance}
              onChange={(e) => setMaxDistance(parseInt(e.target.value))}
              style={{ width: '130px', marginRight: '10px' }}
            />
            <span style={{ width: '40px', display: 'inline-block' }}>{maxDistance}</span>
          </div>
          
          <div style={{ marginBottom: '8px' }}>
            <label style={{ display: 'inline-block', width: '100px' }}>Min Angle:</label>
            <input
              type="range"
              min="0"
              max="90"
              step="5"
              value={minPolarAngle * 180 / Math.PI}
              onChange={(e) => setMinPolarAngle(parseInt(e.target.value) * Math.PI / 180)}
              style={{ width: '130px', marginRight: '10px' }}
            />
            <span style={{ width: '40px', display: 'inline-block' }}>{Math.round(minPolarAngle * 180 / Math.PI)}°</span>
          </div>
          
          <div style={{ marginBottom: '15px' }}>
            <label style={{ display: 'inline-block', width: '100px' }}>Max Angle:</label>
            <input
              type="range"
              min="90"
              max="180"
              step="5"
              value={maxPolarAngle * 180 / Math.PI}
              onChange={(e) => setMaxPolarAngle(parseInt(e.target.value) * Math.PI / 180)}
              style={{ width: '130px', marginRight: '10px' }}
            />
            <span style={{ width: '40px', display: 'inline-block' }}>{Math.round(maxPolarAngle * 180 / Math.PI)}°</span>
          </div>
          
          <button
            onClick={resetView}
            style={{
              width: '100%',
              padding: '8px',
              backgroundColor: '#ff9234',
              color: '#111',
              border: 'none',
              borderRadius: '3px',
              cursor: 'pointer',
              fontWeight: 'bold',
              fontSize: '14px'
            }}
            onMouseOver={(e) => e.target.style.backgroundColor = '#ffb732'}
            onMouseOut={(e) => e.target.style.backgroundColor = '#ff9234'}
          >
            Reset View
          </button>
        </div>
        
        <p style={{ 
          fontSize: '12px',
          marginTop: '10px',
          color: '#999'
        }}>
          Left-drag: rotate • Right-drag: pan • Scroll: zoom • Hover: highlight
        </p>
      </div>

      {/* Legend */}
      <div style={{
        backgroundColor: 'rgba(30, 30, 30, 0.85)',
        padding: '10px 15px',
        borderRadius: '5px',
        border: '1px solid #444',
        minWidth: '200px',
        alignSelf: 'flex-start'
      }}>
        <h4 style={{ 
          marginTop: 0, 
          marginBottom: '8px', 
          fontSize: '14px',
          borderBottom: '1px solid #444',
          paddingBottom: '5px'
        }}>
          Legend
        </h4>
        
        {auctionData.map((trader, i) => (
          <div key={i} style={{ 
            display: 'flex', 
            alignItems: 'center',
            marginBottom: '5px',
            fontSize: '12px',
            fontWeight: hoveredTrader?.index === i ? 'bold' : 'normal'
          }}>
            <span style={{
              display: 'inline-block',
              width: '12px',
              height: '12px',
              marginRight: '8px',
              border: '1px solid #666',
              backgroundColor: `#${trader.color.toString(16).padStart(6, '0')}`
            }} />
            {trader.traderId}
          </div>
        ))}
        
        {hoveredTrader && (
          <div style={{
            marginTop: '15px',
            paddingTop: '10px',
            borderTop: '1px solid #444',
            fontSize: '12px'
          }}>
            <h5 style={{ margin: '0 0 5px 0' }}>{hoveredTrader.traderId}</h5>
            <div>Start: {hoveredTrader.bids[0].quantity}</div>
            <div>End: {hoveredTrader.bids[hoveredTrader.bids.length - 1].quantity}</div>
            <div>Decrease: {hoveredTrader.bids[0].quantity - hoveredTrader.bids[hoveredTrader.bids.length - 1].quantity}</div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ThreeLineChart3Dv2;