import { useMemo } from 'react';
import { Canvas } from '@react-three/fiber';
import { OrbitControls, Line, Text } from '@react-three/drei';
import * as THREE from 'three';

function generateSpiralData(offsetY = 0, twists = 5, radius = 5, points = 200, height = 10) {
  const data = [];
  for (let i = 0; i <= points; i++) {
    const t = (i / points) * Math.PI * 2 * twists;
    const x = Math.cos(t) * radius;
    const z = Math.sin(t) * radius;
    const y = (i / points) * height - height / 2 + offsetY;
    data.push(new THREE.Vector3(x, y, z));
  }
  return data;
}

function Axes({ size = 10 }) {
  const fontProps = { fontSize: 0.7, anchorX: 'center' as const, anchorY: 'middle' as const };
  return (
    <>
      <Line points={[[0, 0, 0], [size, 0, 0]]} color="red" lineWidth={2} />
      <Text position={[size + 1, 0, 0]} color="red" {...fontProps}>X</Text>
      
      <Line points={[[0, 0, 0], [0, size, 0]]} color="green" lineWidth={2} />
      <Text position={[0, size + 1, 0]} color="green" {...fontProps}>Y</Text>
      
      <Line points={[[0, 0, 0], [0, 0, size]]} color="blue" lineWidth={2} />
      <Text position={[0, 0, size + 1]} color="blue" {...fontProps}>Z</Text>
    </>
  );
}

function My3DLineChart() {
  const line1Points = useMemo(() => generateSpiralData(0, 5, 5, 200, 10), []);
  const line2Points = useMemo(() => generateSpiralData(2, 3, 4, 150, 8), []);
  const line3Points = useMemo(() => {
    const points = [];
    for (let i = 0; i < 100; i++) {
      points.push(new THREE.Vector3(
        Math.sin(i * 0.1) * 6,
        Math.cos(i * 0.15) * 3 + i * 0.05 - 2.5,
        Math.sin(i * 0.05) * 5
      ));
    }
    return points;
  }, []);

  return (
    <Canvas camera={{ position: [15, 15, 15] }}>
      <ambientLight intensity={0.6} />
      <directionalLight position={[10, 10, 5]} intensity={1} />
      <OrbitControls enablePan={true} enableZoom={true} enableRotate={true} />
      
      <Line points={line1Points} color="hotpink" lineWidth={3} />
      <Line points={line2Points} color="cyan" lineWidth={3} />
      <Line points={line3Points} color="yellow" lineWidth={3} />
      
      <Axes size={10} />
    </Canvas>
  );
}

export default My3DLineChart;
