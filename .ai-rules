Modifiers:
"Include as many relevant features and interactions as possible" - scope modifier
"Go beyond the basics" - depth modifier
"fully-featured implementation" - completeness modifier Modifiers

Don't hold back, give it your all. 
The goal is to create a production-ready component that showcases the full capabilities of the design system.

“Include as many relevant features and interactions as possible” 
“Add thoughtful details like hover states, transitions, and micro-interactions” 
“Apply design principles: hierarchy, contrast, balance, and movement"

If you create any temporary new files, scripts, or helper files for iteration, clean up these files by removing them at the end of the task.

After receiving tool results, carefully reflect on their quality and determine optimal next steps before proceeding. 
Use your thinking to plan and iterate based on this new information, and then take the best next action.

For maximum efficiency, whenever you need to perform multiple independent operations, invoke all relevant tools simultaneously rather than sequentially.

